#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奖励配置管理器
负责加载、保存、验证奖励配置文件
"""

import os
import sys
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import shutil

class RewardConfigManager:
    """奖励配置管理器"""
    
    def __init__(self, config_dir: str = "data/config/rewards"):
        # 使用get_resource_path获取正确的配置目录路径
        self.config_dir = self.get_resource_path(config_dir)
        # config_dir已在上面设置
        self.backup_dir = os.path.join(self.config_dir, "backups")
        self.main_config_file = os.path.join(self.config_dir, "config.json")
        self.configs = {}
        self.file_timestamps = {}
        
        # 设置日志
        self.logger = logging.getLogger("RewardConfigManager")
        self.logger.setLevel(logging.INFO)
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载主配置
        self._load_main_config()
        
        # 加载所有配置文件
        self.reload_all_configs()
    
    def get_resource_path(self, relative_path):
        """获取资源文件的绝对路径，兼容PyInstaller打包环境"""
        try:
            # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
            base_path = sys._MEIPASS
        except AttributeError:
            # 开发环境下使用当前目录
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        return os.path.join(base_path, relative_path)
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def _load_main_config(self):
        """加载主配置文件"""
        try:
            if os.path.exists(self.main_config_file):
                with open(self.main_config_file, 'r', encoding='utf-8') as f:
                    self.main_config = json.load(f)
            else:
                # 创建默认主配置
                self.main_config = self._get_default_main_config()
                self._save_main_config()
        except Exception as e:
            self.logger.error(f"加载主配置失败: {e}")
            self.main_config = self._get_default_main_config()
    
    def _get_default_main_config(self) -> Dict[str, Any]:
        """获取默认主配置"""
        return {
            "version": "1.0.0",
            "description": "奖励系统主配置文件",
            "config_files": {
                "level_up": {
                    "file": "level_up_rewards.json",
                    "description": "升级奖励配置",
                    "enabled": True,
                    "last_modified": None,
                    "version": "1.0.0"
                },
                "welcome": {
                    "file": "welcome_rewards.json",
                    "description": "欢迎奖励配置",
                    "enabled": True,
                    "last_modified": None,
                    "version": "1.0.0"
                },
                "special": {
                    "file": "special_rewards.json",
                    "description": "特殊奖励配置",
                    "enabled": True,
                    "last_modified": None,
                    "version": "1.0.0"
                }
            },
            "global_settings": {
                "hot_reload_enabled": True,
                "config_check_interval": 30,
                "backup_enabled": True,
                "max_backup_files": 10,
                "log_level": "INFO"
            },
            "validation_rules": {
                "max_gold_reward": 100000,
                "max_experience_reward": 50000,
                "max_items_per_reward": 20,
                "required_fields": ["version", "description"]
            },
            "mail_settings": {
                "default_expire_days": 7,
                "max_mail_content_length": 1000,
                "sender_name": "系统"
            }
        }
    
    def _save_main_config(self):
        """保存主配置文件"""
        try:
            with open(self.main_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.main_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"初始化配置目录失败: {e}")
            raise
    
    def load_main_config(self) -> Optional[Dict[str, Any]]:
        """
        加载主配置文件
        
        Returns:
            主配置数据字典，失败返回None
        """
        try:
            main_config_file = os.path.join(self.config_dir, "config.json")
            
            if not os.path.exists(main_config_file):
                self.logger.error(f"主配置文件不存在: {main_config_file}")
                return None
            
            with open(main_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.logger.info("成功加载主配置")
            return config_data
            
        except Exception as e:
            self.logger.error(f"加载主配置失败: {e}")
            return None
    
    def load_config(self, config_type: str, force_reload: bool = False) -> Optional[Dict[str, Any]]:
        """加载指定类型的配置"""
        try:
            if config_type not in self.main_config["config_files"]:
                self.logger.error(f"未知的配置类型: {config_type}")
                return None
            
            config_info = self.main_config["config_files"][config_type]
            if not config_info["enabled"]:
                self.logger.warning(f"配置类型 {config_type} 已禁用")
                return None
            
            config_file = os.path.join(self.config_dir, config_info["file"])
            
            if not os.path.exists(config_file):
                self.logger.error(f"配置文件不存在: {config_file}")
                return None
            
            # 检查文件时间戳
            file_mtime = os.path.getmtime(config_file)
            if not force_reload and config_type in self.file_timestamps:
                if file_mtime == self.file_timestamps[config_type]:
                    # 文件未修改，返回缓存的配置
                    return self.configs.get(config_type)
            
            # 加载配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证配置
            if self.validate_config(config_data):
                self.configs[config_type] = config_data
                self.file_timestamps[config_type] = file_mtime
                
                # 更新主配置中的修改时间
                self.main_config["config_files"][config_type]["last_modified"] = datetime.now().isoformat()
                self._save_main_config()
                
                if force_reload:
                    self.logger.info(f"强制重新加载配置: {config_type}")
                else:
                    self.logger.info(f"配置 {config_type} 加载成功")
                return config_data
            else:
                self.logger.error(f"配置验证失败: {config_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"加载配置失败 {config_type}: {e}")
            return None
    
    def save_config(self, config_type: str, config_data: Dict[str, Any]) -> bool:
        """保存配置"""
        try:
            if config_type not in self.main_config["config_files"]:
                self.logger.error(f"未知的配置类型: {config_type}")
                return False
            
            # 验证配置
            if not self.validate_config(config_data):
                self.logger.error(f"配置验证失败: {config_type}")
                return False
            
            config_info = self.main_config["config_files"][config_type]
            config_file = os.path.join(self.config_dir, config_info["file"])
            
            # 备份现有配置
            if os.path.exists(config_file) and self.main_config["global_settings"]["backup_enabled"]:
                self._backup_config(config_type)
            
            # 保存新配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            # 更新缓存和时间戳
            self.configs[config_type] = config_data
            self.file_timestamps[config_type] = os.path.getmtime(config_file)
            
            # 更新主配置
            self.main_config["config_files"][config_type]["last_modified"] = datetime.now().isoformat()
            self.main_config["config_files"][config_type]["version"] = config_data.get("version", "1.0.0")
            self._save_main_config()
            
            self.logger.info(f"配置 {config_type} 保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败 {config_type}: {e}")
            return False
    
    def validate_config(self, config_data: Dict[str, Any]) -> bool:
        """验证配置数据"""
        try:
            validation_rules = self.main_config["validation_rules"]
            
            # 检查必需字段
            for field in validation_rules["required_fields"]:
                if field not in config_data:
                    self.logger.error(f"缺少必需字段: {field}")
                    return False
            
            # 验证奖励数值限制
            if "rewards" in config_data:
                rewards = config_data["rewards"]
                if isinstance(rewards, dict):
                    if "gold" in rewards and rewards["gold"] > validation_rules["max_gold_reward"]:
                        self.logger.error(f"金币奖励超过限制: {rewards['gold']}")
                        return False
                    
                    if "experience" in rewards and rewards["experience"] > validation_rules["max_experience_reward"]:
                        self.logger.error(f"经验奖励超过限制: {rewards['experience']}")
                        return False
                    
                    if "items" in rewards and isinstance(rewards["items"], list):
                        if len(rewards["items"]) > validation_rules["max_items_per_reward"]:
                            self.logger.error(f"物品数量超过限制: {len(rewards['items'])}")
                            return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证异常: {e}")
            return False
    
    def _backup_config(self, config_type: str):
        """备份配置文件"""
        try:
            config_info = self.main_config["config_files"][config_type]
            config_file = os.path.join(self.config_dir, config_info["file"])
            
            if not os.path.exists(config_file):
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{config_type}_{timestamp}.json"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            shutil.copy2(config_file, backup_path)
            
            # 清理旧备份
            self._cleanup_old_backups(config_type)
            
            self.logger.info(f"配置备份成功: {backup_filename}")
            
        except Exception as e:
            self.logger.error(f"备份配置失败 {config_type}: {e}")
    
    def _cleanup_old_backups(self, config_type: str):
        """清理旧备份文件"""
        try:
            max_backups = self.main_config["global_settings"]["max_backup_files"]
            
            # 获取该配置类型的所有备份文件
            backup_files = []
            for filename in os.listdir(self.backup_dir):
                if filename.startswith(f"{config_type}_") and filename.endswith(".json"):
                    filepath = os.path.join(self.backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))
            
            # 按修改时间排序，保留最新的文件
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出限制的旧文件
            for filepath, _ in backup_files[max_backups:]:
                os.remove(filepath)
                self.logger.info(f"删除旧备份: {os.path.basename(filepath)}")
                
        except Exception as e:
            self.logger.error(f"清理备份失败: {e}")
    
    def reload_all_configs(self):
        """重新加载所有配置"""
        self.logger.info("重新加载所有配置...")
        
        for config_type in self.main_config["config_files"].keys():
            self.load_config(config_type)
    
    def get_config_version(self, config_type: str = None) -> str:
        """获取配置版本"""
        if config_type:
            config = self.configs.get(config_type)
            return config.get("version", "unknown") if config else "unknown"
        else:
            return self.main_config.get("version", "unknown")
    
    def get_all_config_types(self) -> List[str]:
        """获取所有配置类型"""
        return list(self.main_config["config_files"].keys())
    
    def is_config_enabled(self, config_type: str) -> bool:
        """检查配置是否启用"""
        if config_type in self.main_config["config_files"]:
            return self.main_config["config_files"][config_type]["enabled"]
        return False
    
    def set_config_enabled(self, config_type: str, enabled: bool) -> bool:
        """设置配置启用状态"""
        try:
            if config_type in self.main_config["config_files"]:
                self.main_config["config_files"][config_type]["enabled"] = enabled
                self._save_main_config()
                self.logger.info(f"配置 {config_type} 启用状态设置为: {enabled}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"设置配置启用状态失败: {e}")
            return False
    
    def check_for_updates(self) -> List[str]:
        """检查配置文件更新"""
        updated_configs = []
        
        for config_type in self.main_config["config_files"].keys():
            config_info = self.main_config["config_files"][config_type]
            config_file = os.path.join(self.config_dir, config_info["file"])
            
            if os.path.exists(config_file):
                file_mtime = os.path.getmtime(config_file)
                if config_type not in self.file_timestamps or file_mtime > self.file_timestamps[config_type]:
                    updated_configs.append(config_type)
        
        return updated_configs