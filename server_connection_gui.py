#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器连接管理GUI
让玩家可以连接到云端服务器，启用邮件和聊天功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
from typing import Dict, List
from cloud_connection_manager import get_cloud_manager

class ServerConnectionGUI:
    """服务器连接管理GUI"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.cloud_manager = get_cloud_manager()
        self.window = None
        self.is_destroyed = False
        
        # 服务器配置
        self.server_config_file = "data/config/server_config.json"
        self.server_configs = self.load_server_configs()
        
        self.setup_gui()
        self.update_connection_status()
    
    def load_server_configs(self) -> Dict:
        """加载服务器配置"""
        try:
            if os.path.exists(self.server_config_file):
                with open(self.server_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载服务器配置失败: {e}")
        
        # 默认配置
        return {
            "default_server": "http://**************:8000",
            "backup_servers": [
                "http://**************:8000",
                "http://localhost:8000"
            ],
            "auto_connect": False,
            "last_username": "",
            "version": "1.0.0"
        }
    
    def save_server_configs(self):
        """保存服务器配置"""
        try:
            os.makedirs(os.path.dirname(self.server_config_file), exist_ok=True)
            with open(self.server_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.server_configs, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存服务器配置失败: {e}")
    
    def setup_gui(self):
        """设置GUI界面"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("服务器连接管理")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        
        # 设置窗口图标（如果存在）
        try:
            self.window.iconbitmap("game_icon.ico")
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="云端服务器连接", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 连接状态显示
        self.create_status_section(main_frame)
        
        # 服务器选择
        self.create_server_section(main_frame)
        
        # 用户认证
        self.create_auth_section(main_frame)
        
        # 操作按钮
        self.create_action_buttons(main_frame)
        
        # 说明文字
        self.create_info_section(main_frame)
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 定期更新状态
        self.update_status_loop()
    
    def create_status_section(self, parent):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(parent, text="连接状态", padding=10)
        status_frame.pack(fill="x", pady=(0, 15))
        
        # 状态指示器
        status_info_frame = ttk.Frame(status_frame)
        status_info_frame.pack(fill="x")
        
        ttk.Label(status_info_frame, text="状态:").pack(side="left")
        self.status_label = ttk.Label(status_info_frame, text="", font=("Arial", 10, "bold"))
        self.status_label.pack(side="left", padx=(10, 0))
        
        # 用户信息
        self.user_info_frame = ttk.Frame(status_frame)
        self.user_info_frame.pack(fill="x", pady=(10, 0))
        
        self.user_info_label = ttk.Label(self.user_info_frame, text="")
        self.user_info_label.pack(side="left")
        
        # 服务器信息
        self.server_info_frame = ttk.Frame(status_frame)
        self.server_info_frame.pack(fill="x", pady=(5, 0))
        
        self.server_info_label = ttk.Label(self.server_info_frame, text="")
        self.server_info_label.pack(side="left")
    
    def create_server_section(self, parent):
        """创建服务器选择区域"""
        server_frame = ttk.LabelFrame(parent, text="服务器设置", padding=10)
        server_frame.pack(fill="x", pady=(0, 15))
        
        # 服务器地址
        addr_frame = ttk.Frame(server_frame)
        addr_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(addr_frame, text="服务器地址:").pack(side="left")
        self.server_var = tk.StringVar(value=self.server_configs.get("default_server", ""))
        self.server_combo = ttk.Combobox(addr_frame, textvariable=self.server_var, width=40)
        self.server_combo['values'] = self.server_configs.get("backup_servers", [])
        self.server_combo.pack(side="left", padx=(10, 5), fill="x", expand=True)
        
        ttk.Button(addr_frame, text="测试", command=self.test_server_connection, 
                  width=8).pack(side="right")
    
    def create_auth_section(self, parent):
        """创建用户认证区域"""
        auth_frame = ttk.LabelFrame(parent, text="用户认证", padding=10)
        auth_frame.pack(fill="x", pady=(0, 15))
        
        # 用户名
        username_frame = ttk.Frame(auth_frame)
        username_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(username_frame, text="用户名:", width=10).pack(side="left")
        self.username_var = tk.StringVar(value=self.server_configs.get("last_username", ""))
        self.username_entry = ttk.Entry(username_frame, textvariable=self.username_var)
        self.username_entry.pack(side="left", padx=(10, 0), fill="x", expand=True)
        
        # 密码
        password_frame = ttk.Frame(auth_frame)
        password_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(password_frame, text="密码:", width=10).pack(side="left")
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(password_frame, textvariable=self.password_var, show="*")
        self.password_entry.pack(side="left", padx=(10, 0), fill="x", expand=True)
        
        # 记住用户名选项
        self.remember_var = tk.BooleanVar(value=bool(self.server_configs.get("last_username")))
        self.remember_check = ttk.Checkbutton(auth_frame, text="记住用户名", 
                                            variable=self.remember_var)
        self.remember_check.pack(anchor="w")
    
    def create_action_buttons(self, parent):
        """创建操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill="x", pady=(0, 15))
        
        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side="left")
        
        self.connect_button = ttk.Button(left_buttons, text="连接服务器", 
                                       command=self.connect_to_server, width=12)
        self.connect_button.pack(side="left", padx=(0, 10))
        
        self.disconnect_button = ttk.Button(left_buttons, text="断开连接", 
                                          command=self.disconnect_from_server, 
                                          width=12, state="disabled")
        self.disconnect_button.pack(side="left", padx=(0, 10))
        
        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side="right")
        
        ttk.Button(right_buttons, text="注册账号", command=self.register_account, 
                  width=10).pack(side="left", padx=(10, 0))
        
        ttk.Button(right_buttons, text="关闭", command=self.on_close, 
                  width=8).pack(side="left", padx=(10, 0))
    
    def create_info_section(self, parent):
        """创建说明信息区域"""
        info_frame = ttk.LabelFrame(parent, text="使用说明", padding=10)
        info_frame.pack(fill="x")
        
        info_text = """
• 连接到云端服务器后，可以使用邮件系统和聊天功能
• 本地模式下，邮件和聊天功能将被禁用
• 首次使用需要注册账号
• 连接成功后，邮件和聊天数据将同步到云端
        """.strip()
        
        info_label = ttk.Label(info_frame, text=info_text, justify="left", 
                              font=("Arial", 9))
        info_label.pack(anchor="w")
    
    def update_connection_status(self):
        """更新连接状态显示"""
        if self.cloud_manager.is_server_connected():
            self.status_label.config(text="已连接", foreground="green")
            
            # 显示用户信息
            user_info = self.cloud_manager.user_info
            if user_info:
                self.user_info_label.config(text=f"用户: {user_info.get('username', '未知')}")
            
            # 显示服务器信息
            self.server_info_label.config(text=f"服务器: {self.cloud_manager.server_url}")
            
            # 更新按钮状态
            self.connect_button.config(state="disabled")
            self.disconnect_button.config(state="normal")
            
        else:
            self.status_label.config(text="未连接", foreground="red")
            self.user_info_label.config(text="")
            self.server_info_label.config(text="")
            
            # 更新按钮状态
            self.connect_button.config(state="normal")
            self.disconnect_button.config(state="disabled")
    
    def update_status_loop(self):
        """定期更新状态"""
        if self.is_destroyed:
            return
        
        self.update_connection_status()
        
        # 每2秒更新一次
        if self.window and not self.is_destroyed:
            self.window.after(2000, self.update_status_loop)
    
    def test_server_connection(self):
        """测试服务器连接"""
        server_url = self.server_var.get().strip()
        if not server_url:
            messagebox.showwarning("警告", "请输入服务器地址")
            return
        
        try:
            import requests
            response = requests.get(server_url, timeout=5)
            if response.status_code == 200:
                messagebox.showinfo("成功", "服务器连接测试成功！")
            else:
                messagebox.showerror("错误", f"服务器响应错误: {response.status_code}")
        except Exception as e:
            messagebox.showerror("错误", f"连接测试失败: {str(e)}")
    
    def connect_to_server(self):
        """连接到服务器"""
        server_url = self.server_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not server_url:
            messagebox.showwarning("警告", "请输入服务器地址")
            return
        
        if not username:
            messagebox.showwarning("警告", "请输入用户名")
            return
        
        if not password:
            messagebox.showwarning("警告", "请输入密码")
            return
        
        try:
            # 设置服务器地址
            self.cloud_manager.set_server_url(server_url)
            
            # 尝试连接
            success = self.cloud_manager.connect_to_server(username, password)
            
            if success:
                messagebox.showinfo("成功", "服务器连接成功！\n现在可以使用邮件和聊天功能了。")
                
                # 保存配置
                self.server_configs["default_server"] = server_url
                if server_url not in self.server_configs["backup_servers"]:
                    self.server_configs["backup_servers"].append(server_url)
                
                if self.remember_var.get():
                    self.server_configs["last_username"] = username
                else:
                    self.server_configs["last_username"] = ""
                
                self.save_server_configs()
                
                # 更新状态
                self.update_connection_status()
                
            else:
                messagebox.showerror("错误", "连接服务器失败！\n请检查服务器地址和登录信息。")
                
        except Exception as e:
            messagebox.showerror("错误", f"连接失败: {str(e)}")
    
    def disconnect_from_server(self):
        """断开服务器连接"""
        if messagebox.askyesno("确认断开", "确定要断开服务器连接吗？\n断开后邮件和聊天功能将不可用。"):
            self.cloud_manager.disconnect_from_server()
            self.update_connection_status()
            messagebox.showinfo("已断开", "已断开服务器连接")
    
    def register_account(self):
        """注册新账号"""
        # 创建注册窗口
        register_window = tk.Toplevel(self.window)
        register_window.title("注册新账号")
        register_window.geometry("400x300")
        register_window.resizable(False, False)
        
        # 设置为模态窗口
        register_window.transient(self.window)
        register_window.grab_set()
        
        main_frame = ttk.Frame(register_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        ttk.Label(main_frame, text="注册新账号", font=("Arial", 12, "bold")).pack(pady=(0, 20))
        
        # 服务器地址
        server_frame = ttk.Frame(main_frame)
        server_frame.pack(fill="x", pady=(0, 10))
        ttk.Label(server_frame, text="服务器地址:", width=12).pack(side="left")
        register_server_var = tk.StringVar(value=self.server_var.get())
        ttk.Entry(server_frame, textvariable=register_server_var).pack(side="left", fill="x", expand=True, padx=(10, 0))
        
        # 用户名
        username_frame = ttk.Frame(main_frame)
        username_frame.pack(fill="x", pady=(0, 10))
        ttk.Label(username_frame, text="用户名:", width=12).pack(side="left")
        register_username_var = tk.StringVar()
        ttk.Entry(username_frame, textvariable=register_username_var).pack(side="left", fill="x", expand=True, padx=(10, 0))
        
        # 密码
        password_frame = ttk.Frame(main_frame)
        password_frame.pack(fill="x", pady=(0, 10))
        ttk.Label(password_frame, text="密码:", width=12).pack(side="left")
        register_password_var = tk.StringVar()
        ttk.Entry(password_frame, textvariable=register_password_var, show="*").pack(side="left", fill="x", expand=True, padx=(10, 0))
        
        # 确认密码
        confirm_frame = ttk.Frame(main_frame)
        confirm_frame.pack(fill="x", pady=(0, 20))
        ttk.Label(confirm_frame, text="确认密码:", width=12).pack(side="left")
        confirm_password_var = tk.StringVar()
        ttk.Entry(confirm_frame, textvariable=confirm_password_var, show="*").pack(side="left", fill="x", expand=True, padx=(10, 0))
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")
        
        def do_register():
            server_url = register_server_var.get().strip()
            username = register_username_var.get().strip()
            password = register_password_var.get().strip()
            confirm_password = confirm_password_var.get().strip()
            
            if not all([server_url, username, password, confirm_password]):
                messagebox.showwarning("警告", "请填写所有字段")
                return
            
            if password != confirm_password:
                messagebox.showerror("错误", "两次输入的密码不一致")
                return
            
            try:
                import requests
                response = requests.post(f"{server_url}/auth/register", 
                                       json={"username": username, "password": password},
                                       timeout=10)
                
                if response.status_code == 200:
                    messagebox.showinfo("成功", "账号注册成功！\n现在可以使用该账号登录了。")
                    
                    # 自动填充登录信息
                    self.server_var.set(server_url)
                    self.username_var.set(username)
                    self.password_var.set(password)
                    
                    register_window.destroy()
                else:
                    error_msg = response.json().get("detail", "注册失败")
                    messagebox.showerror("错误", f"注册失败: {error_msg}")
                    
            except Exception as e:
                messagebox.showerror("错误", f"注册失败: {str(e)}")
        
        ttk.Button(button_frame, text="注册", command=do_register, width=10).pack(side="left")
        ttk.Button(button_frame, text="取消", command=register_window.destroy, width=10).pack(side="left", padx=(10, 0))
    
    def on_close(self):
        """关闭窗口"""
        if self.window:
            self.is_destroyed = True
            self.window.destroy()
            self.window = None

def show_server_connection_gui(parent=None):
    """显示服务器连接管理GUI"""
    return ServerConnectionGUI(parent) 