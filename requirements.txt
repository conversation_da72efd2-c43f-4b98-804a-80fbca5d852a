# 基础游戏依赖
tkinter  # 通常Python自带

# 系统信息
psutil>=5.9.0

# 可选依赖 - 存档加密功能
cryptography>=41.0.0  # 可选，用于存档加密

# 云端功能依赖（可选）
requests>=2.31.0      # 用于HTTP请求
websockets>=12.0      # 用于WebSocket通信

# 注意：
# - cryptography 是可选依赖，如果没有安装，加密功能会被禁用
# - requests 和 websockets 是云端功能必需的
# - 如果只使用本地功能，只需要 psutil

# 安装命令：
# 基本功能：pip install psutil
# 加密功能：pip install psutil cryptography
# 云端功能：pip install psutil requests websockets
# 完整功能：pip install -r requirements.txt 