#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本
删除无用的本地邮件系统和重复的存档系统文件
"""

import os
import shutil
import glob

def safe_delete(path):
    """安全删除文件或文件夹"""
    try:
        if os.path.isfile(path):
            os.remove(path)
            print(f"✅ 删除文件: {path}")
        elif os.path.isdir(path):
            shutil.rmtree(path)
            print(f"✅ 删除文件夹: {path}")
        else:
            print(f"⚠️  路径不存在: {path}")
    except Exception as e:
        print(f"❌ 删除失败 {path}: {e}")

def cleanup_project():
    """清理项目文件"""
    print("🧹 开始清理项目无用文件...")
    
    # 1. 本地邮件系统文件
    mail_files = [
        "mail_integration.py",
        "local_mail_gui.py", 
        "init_mail_database.py",
        "mail_reward_service.py",
        "optimized_mail_manager.py",
        "cleanup_duplicate_mails.py",
        "check_mails.py",
        "test_mail_claim_feature.py",
    ]
    
    print("\n📧 清理本地邮件系统文件...")
    for file in mail_files:
        safe_delete(file)
    
    # 2. 本地数据库文件
    db_files = [
        "local_mails.db",
        "game_database.db",
        "game_database.db-shm", 
        "game_database.db-wal",
        "local_database.py",
        "user_database.py",
    ]
    
    print("\n💾 清理本地数据库文件...")
    for file in db_files:
        safe_delete(file)
    
    # 3. 重复的存档系统文件
    save_files = [
        "save_game_manager.py",
        "enhanced_save_system.py",
        "integrated_save_system.py", 
        "migrate_to_new_save_system.py",
        "storage_implementations.py",
        "save_system_config.py",
        "save_system_config.json",
    ]
    
    print("\n💾 清理重复的存档系统文件...")
    for file in save_files:
        safe_delete(file)
    
    # 4. 开发和调试工具
    tool_files = [
        "aes_tool.py",
        "player_data_validator.py",
        "hot_reload_manager.py", 
        "optimization_manager.py",
        "optimization_config.json",
    ]
    
    print("\n🔧 清理开发工具文件...")
    for file in tool_files:
        safe_delete(file)
    
    # 5. 构建相关文件
    build_files = [
        "build_client.py",
        "client.spec",
        "job_stats.json",
    ]
    
    print("\n🏗️ 清理构建文件...")
    for file in build_files:
        safe_delete(file)
    
    # 6. Web部署文件
    web_files = [
        "vercel.json",
        ".vercelignore",
    ]
    
    print("\n🌐 清理Web部署文件...")
    for file in web_files:
        safe_delete(file)
    
    # 7. 构建文件夹
    build_dirs = [
        "build",
        "dist", 
        "__pycache__",
        ".trae",
    ]
    
    print("\n📁 清理构建文件夹...")
    for dir_name in build_dirs:
        safe_delete(dir_name)
    
    # 8. 旧版本文件夹
    old_versions = glob.glob("传奇游戏客户端_v*")
    
    print("\n📦 清理旧版本文件夹...")
    for version_dir in old_versions:
        safe_delete(version_dir)
    
    # 9. 虚拟环境
    venv_dirs = [".venv", "venv", "env"]
    
    print("\n🐍 清理虚拟环境文件夹...")
    for venv_dir in venv_dirs:
        if os.path.exists(venv_dir):
            safe_delete(venv_dir)
    
    print("\n✨ 项目清理完成！")
    print("\n📋 保留的核心文件:")
    print("   ✅ main.py - 主程序")
    print("   ✅ game_admin.py - 游戏管理")  
    print("   ✅ enhancement_system.py - 装备强化")
    print("   ✅ cloud_*.py - 云端系统文件")
    print("   ✅ server_connection_gui.py - 服务器连接")
    print("   ✅ database_*.py - 数据库优化")
    print("   ✅ reward_*.py - 奖励系统")
    print("   ✅ data/ - 游戏数据")
    print("   ✅ config/ - 配置文件")
    print("   ✅ saves/ - 存档文件")
    print("   ✅ local_saves/ - 本地存档")
    print("   ✅ game_data/ - 游戏数据")
    print("   ✅ release/ - 发布文件")

def show_final_structure():
    """显示清理后的文件结构"""
    print("\n📊 清理后的项目结构:")
    print("游戏客户端/")
    print("├── main.py                      # 主程序")
    print("├── game_admin.py               # 游戏管理界面")
    print("├── enhancement_system.py       # 装备强化系统")
    print("├── game_icon.ico              # 游戏图标")
    print("├── cloud_server.py            # 云端服务器")
    print("├── cloud_connection_manager.py # 云连接管理器") 
    print("├── cloud_mail_gui.py          # 云端邮件GUI")
    print("├── cloud_chat_gui.py          # 云端聊天GUI")
    print("├── server_connection_gui.py    # 服务器连接GUI")
    print("├── database_optimization.py    # 数据库优化")
    print("├── database_pool.py           # 数据库连接池")
    print("├── reward_config_manager.py    # 奖励配置管理器")
    print("├── reward_config_gui.py       # 奖励配置GUI")
    print("├── requirements_cloud.txt      # 云端依赖")
    print("├── 云端系统部署指南.md         # 部署文档")
    print("├── data/                      # 游戏数据")
    print("├── config/                    # 配置文件")
    print("├── saves/                     # 存档文件")
    print("├── local_saves/               # 本地存档")
    print("├── game_data/                 # 游戏数据")
    print("└── release/                   # 发布文件")

if __name__ == "__main__":
    print("⚠️  警告：此脚本将删除大量文件！")
    confirm = input("确定要继续清理吗？(输入 'yes' 确认): ")
    
    if confirm.lower() == 'yes':
        cleanup_project()
        show_final_structure()
    else:
        print("❌ 清理已取消") 