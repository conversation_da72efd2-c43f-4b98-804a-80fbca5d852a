#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传奇游戏管理器
功能：玩家管理、数据修改、服务器控制、游戏数据管理
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import json
import sqlite3
import requests
import threading
import time
import os
import sys
from datetime import datetime
import subprocess

class GameAdminGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("传奇游戏管理器 - 管理员控制台")
        self.root.geometry("1200x800")
        
        # 服务器配置
        self.server_url = "http://117.72.223.160:8000"
        self.admin_token = None
        
        # 数据库连接
        self.local_db_path = "game_database.db"
        self.users_db_path = "users.db"
        
        # 创建界面
        self.create_widgets()
        
        # 启动状态监控
        self.start_monitoring()
    
    def get_resource_path(self, relative_path):
        """获取资源文件的绝对路径，兼容PyInstaller打包"""
        try:
            # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
            base_path = sys._MEIPASS
        except Exception:
            # 如果不是打包环境，使用当前脚本目录
            base_path = os.path.abspath(".")
        
        return os.path.join(base_path, relative_path)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主菜单
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_main_content()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 服务器菜单
        server_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="服务器", menu=server_menu)
        server_menu.add_command(label="连接服务器", command=self.connect_server)
        server_menu.add_command(label="断开连接", command=self.disconnect_server)
        server_menu.add_separator()
        server_menu.add_command(label="重启服务器", command=self.restart_server)
        server_menu.add_command(label="关闭服务器", command=self.shutdown_server)
        
        # 数据菜单
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="数据管理", menu=data_menu)
        data_menu.add_command(label="备份数据", command=self.backup_data)
        data_menu.add_command(label="恢复数据", command=self.restore_data)
        data_menu.add_command(label="清理数据", command=self.clean_data)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="日志查看器", command=self.open_log_viewer)
        tools_menu.add_command(label="数据库浏览器", command=self.open_db_browser)
        tools_menu.add_command(label="配置编辑器", command=self.open_config_editor)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(fill=tk.X, padx=5, pady=2)
        
        # 连接状态
        self.connection_label = ttk.Label(toolbar, text="未连接", foreground="red")
        self.connection_label.pack(side=tk.LEFT, padx=5)
        
        # 快速操作按钮
        ttk.Button(toolbar, text="刷新", command=self.refresh_all).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="踢出所有玩家", command=self.kick_all_players).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="服务器公告", command=self.send_announcement).pack(side=tk.LEFT, padx=2)
        
        # 服务器信息
        self.server_info_label = ttk.Label(toolbar, text="服务器信息: 未知")
        self.server_info_label.pack(side=tk.RIGHT, padx=5)
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 玩家管理标签页
        self.create_player_management_tab()
        
        # 数据管理标签页
        self.create_data_management_tab()
        
        # 服务器监控标签页
        self.create_server_monitoring_tab()
        
        # IP管理标签页
        self.create_ip_management_tab()
    
    def create_player_management_tab(self):
        """创建玩家管理标签页"""
        player_frame = ttk.Frame(self.notebook)
        self.notebook.add(player_frame, text="玩家管理")
        
        # 左侧：游戏存档列表
        left_frame = ttk.LabelFrame(player_frame, text="游戏存档")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 玩家列表
        columns = ("ID", "账号", "角色名", "等级", "职业", "位置", "存档时间", "备注")
        self.player_tree = ttk.Treeview(left_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.player_tree.heading(col, text=col)
            self.player_tree.column(col, width=100)
        
        # 滚动条
        player_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.player_tree.yview)
        self.player_tree.configure(yscrollcommand=player_scrollbar.set)
        
        self.player_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        player_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧：玩家操作
        right_frame = ttk.LabelFrame(player_frame, text="玩家操作")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        # 玩家信息显示
        info_frame = ttk.LabelFrame(right_frame, text="玩家信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.player_info_text = tk.Text(info_frame, height=10, width=30)
        self.player_info_text.pack(fill=tk.BOTH, expand=True)
        
        # 操作按钮
        btn_frame = ttk.Frame(right_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="踢出玩家", command=self.kick_player).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="封禁玩家", command=self.ban_player).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="解封玩家", command=self.unban_player).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="修改数据", command=self.edit_player_data).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="发送消息", command=self.send_message_to_player).pack(fill=tk.X, pady=2)
        ttk.Button(btn_frame, text="传送玩家", command=self.teleport_player).pack(fill=tk.X, pady=2)
        
        # 绑定选择事件
        self.player_tree.bind("<<TreeviewSelect>>", self.on_player_select)
    
    def create_data_management_tab(self):
        """创建数据管理标签页"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="数据管理")
        
        # 创建子标签页
        data_notebook = ttk.Notebook(data_frame)
        data_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 怪物数据管理
        self.create_monster_data_tab(data_notebook)
        
        # 装备数据管理
        self.create_equipment_data_tab(data_notebook)
        
        # 地图数据管理
        self.create_map_data_tab(data_notebook)
        
        # 技能数据管理
        self.create_skill_data_tab(data_notebook)
    
    def create_monster_data_tab(self, parent):
        """创建怪物数据管理标签页"""
        monster_frame = ttk.Frame(parent)
        parent.add(monster_frame, text="怪物管理")
        
        # 怪物列表
        left_monster_frame = ttk.LabelFrame(monster_frame, text="怪物列表")
        left_monster_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        monster_columns = ("名称", "等级", "血量", "攻击下限", "攻击上限", "防御", "魔御", "经验")
        self.monster_tree = ttk.Treeview(left_monster_frame, columns=monster_columns, show="headings")
        
        for col in monster_columns:
            self.monster_tree.heading(col, text=col)
            self.monster_tree.column(col, width=80)
        
        monster_scrollbar = ttk.Scrollbar(left_monster_frame, orient=tk.VERTICAL, command=self.monster_tree.yview)
        self.monster_tree.configure(yscrollcommand=monster_scrollbar.set)
        
        self.monster_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monster_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 怪物编辑
        right_monster_frame = ttk.LabelFrame(monster_frame, text="怪物编辑")
        right_monster_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        # 创建滚动框架来容纳所有输入控件
        canvas = tk.Canvas(right_monster_frame, width=200)
        scrollbar = ttk.Scrollbar(right_monster_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 怪物属性编辑框
        ttk.Label(scrollable_frame, text="名称:").pack(anchor=tk.W)
        self.monster_name_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_name_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="等级:").pack(anchor=tk.W)
        self.monster_level_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_level_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="血量:").pack(anchor=tk.W)
        self.monster_hp_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_hp_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="攻击下限:").pack(anchor=tk.W)
        self.monster_attack_min_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_attack_min_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="攻击上限:").pack(anchor=tk.W)
        self.monster_attack_max_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_attack_max_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="防御:").pack(anchor=tk.W)
        self.monster_defense_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_defense_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="魔御:").pack(anchor=tk.W)
        self.monster_magic_defense_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_magic_defense_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="经验值:").pack(anchor=tk.W)
        self.monster_experience_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_experience_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="敏捷:").pack(anchor=tk.W)
        self.monster_agility_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_agility_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="准确:").pack(anchor=tk.W)
        self.monster_accuracy_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_accuracy_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="移动速度:").pack(anchor=tk.W)
        self.monster_move_speed_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_move_speed_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="攻击速度:").pack(anchor=tk.W)
        self.monster_attack_speed_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_attack_speed_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(scrollable_frame, text="不死系 (0/1):").pack(anchor=tk.W)
        self.monster_undead_var = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.monster_undead_var).pack(fill=tk.X, pady=2)
        
        # 操作按钮
        monster_btn_frame = ttk.Frame(scrollable_frame)
        monster_btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(monster_btn_frame, text="保存修改", command=self.save_monster_data).pack(fill=tk.X, pady=2)
        ttk.Button(monster_btn_frame, text="添加怪物", command=self.add_monster).pack(fill=tk.X, pady=2)
        ttk.Button(monster_btn_frame, text="删除怪物", command=self.delete_monster).pack(fill=tk.X, pady=2)
        ttk.Button(monster_btn_frame, text="刷新列表", command=self.refresh_monster_list).pack(fill=tk.X, pady=2)
        
        # 打包滚动框架
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.monster_tree.bind("<<TreeviewSelect>>", self.on_monster_select)
    
    def create_equipment_data_tab(self, parent):
        """创建装备数据管理标签页"""
        equipment_frame = ttk.Frame(parent)
        parent.add(equipment_frame, text="装备管理")
        
        # 装备列表
        left_eq_frame = ttk.LabelFrame(equipment_frame, text="装备列表")
        left_eq_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        eq_columns = ("名称", "类型", "等级", "攻击力", "防御力", "价格")
        self.equipment_tree = ttk.Treeview(left_eq_frame, columns=eq_columns, show="headings")
        
        for col in eq_columns:
            self.equipment_tree.heading(col, text=col)
            self.equipment_tree.column(col, width=80)
        
        eq_scrollbar = ttk.Scrollbar(left_eq_frame, orient=tk.VERTICAL, command=self.equipment_tree.yview)
        self.equipment_tree.configure(yscrollcommand=eq_scrollbar.set)
        
        self.equipment_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        eq_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 装备编辑
        right_eq_frame = ttk.LabelFrame(equipment_frame, text="装备编辑")
        right_eq_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        # 装备属性编辑框
        ttk.Label(right_eq_frame, text="名称:").pack(anchor=tk.W)
        self.eq_name_var = tk.StringVar()
        ttk.Entry(right_eq_frame, textvariable=self.eq_name_var).pack(fill=tk.X, pady=2)
        
        ttk.Label(right_eq_frame, text="类型:").pack(anchor=tk.W)
        self.eq_type_var = tk.StringVar()
        eq_type_combo = ttk.Combobox(right_eq_frame, textvariable=self.eq_type_var)
        eq_type_combo['values'] = ('武器', '头盔', '护甲', '鞋子', '项链', '戒指', '魔石')
        eq_type_combo.pack(fill=tk.X, pady=2)
        
        ttk.Label(right_eq_frame, text="等级要求:").pack(anchor=tk.W)
        self.eq_level_var = tk.StringVar()
        ttk.Entry(right_eq_frame, textvariable=self.eq_level_var).pack(fill=tk.X, pady=2)
        
        # 操作按钮
        eq_btn_frame = ttk.Frame(right_eq_frame)
        eq_btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(eq_btn_frame, text="保存修改", command=self.save_equipment_data).pack(fill=tk.X, pady=2)
        ttk.Button(eq_btn_frame, text="添加装备", command=self.add_equipment).pack(fill=tk.X, pady=2)
        ttk.Button(eq_btn_frame, text="删除装备", command=self.delete_equipment).pack(fill=tk.X, pady=2)
        ttk.Button(eq_btn_frame, text="刷新列表", command=self.refresh_equipment_list).pack(fill=tk.X, pady=2)
        
        # 绑定选择事件
        self.equipment_tree.bind("<<TreeviewSelect>>", self.on_equipment_select)

    def create_map_data_tab(self, parent):
        """创建地图数据管理标签页"""
        map_frame = ttk.Frame(parent)
        parent.add(map_frame, text="地图管理")

        # 地图列表
        left_map_frame = ttk.LabelFrame(map_frame, text="地图列表")
        left_map_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        map_columns = ("名称", "描述", "等级要求", "怪物数量")
        self.map_tree = ttk.Treeview(left_map_frame, columns=map_columns, show="headings")

        for col in map_columns:
            self.map_tree.heading(col, text=col)
            self.map_tree.column(col, width=100)

        map_scrollbar = ttk.Scrollbar(left_map_frame, orient=tk.VERTICAL, command=self.map_tree.yview)
        self.map_tree.configure(yscrollcommand=map_scrollbar.set)

        self.map_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        map_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 地图编辑
        right_map_frame = ttk.LabelFrame(map_frame, text="地图编辑")
        right_map_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)

        # 地图属性编辑
        ttk.Label(right_map_frame, text="地图名称:").pack(anchor=tk.W)
        self.map_name_var = tk.StringVar()
        ttk.Entry(right_map_frame, textvariable=self.map_name_var).pack(fill=tk.X, pady=2)

        ttk.Label(right_map_frame, text="描述:").pack(anchor=tk.W)
        self.map_desc_var = tk.StringVar()
        ttk.Entry(right_map_frame, textvariable=self.map_desc_var).pack(fill=tk.X, pady=2)

        # 操作按钮
        map_btn_frame = ttk.Frame(right_map_frame)
        map_btn_frame.pack(fill=tk.X, pady=10)

        ttk.Button(map_btn_frame, text="保存修改", command=self.save_map_data).pack(fill=tk.X, pady=2)
        ttk.Button(map_btn_frame, text="刷新列表", command=self.refresh_map_list).pack(fill=tk.X, pady=2)

        # 绑定选择事件
        self.map_tree.bind("<<TreeviewSelect>>", self.on_map_select)

    def create_skill_data_tab(self, parent):
        """创建技能数据管理标签页"""
        skill_frame = ttk.Frame(parent)
        parent.add(skill_frame, text="技能管理")

        # 技能列表
        left_skill_frame = ttk.LabelFrame(skill_frame, text="技能列表")
        left_skill_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        skill_columns = ("名称", "类型", "等级", "伤害", "消耗MP")
        self.skill_tree = ttk.Treeview(left_skill_frame, columns=skill_columns, show="headings")

        for col in skill_columns:
            self.skill_tree.heading(col, text=col)
            self.skill_tree.column(col, width=80)

        skill_scrollbar = ttk.Scrollbar(left_skill_frame, orient=tk.VERTICAL, command=self.skill_tree.yview)
        self.skill_tree.configure(yscrollcommand=skill_scrollbar.set)

        self.skill_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        skill_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 技能编辑
        right_skill_frame = ttk.LabelFrame(skill_frame, text="技能编辑")
        right_skill_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)

        # 技能属性编辑
        ttk.Label(right_skill_frame, text="技能名称:").pack(anchor=tk.W)
        self.skill_name_var = tk.StringVar()
        ttk.Entry(right_skill_frame, textvariable=self.skill_name_var).pack(fill=tk.X, pady=2)

        # 操作按钮
        skill_btn_frame = ttk.Frame(right_skill_frame)
        skill_btn_frame.pack(fill=tk.X, pady=10)

        ttk.Button(skill_btn_frame, text="保存修改", command=self.save_skill_data).pack(fill=tk.X, pady=2)
        ttk.Button(skill_btn_frame, text="刷新列表", command=self.refresh_skill_list).pack(fill=tk.X, pady=2)

        # 绑定选择事件
        self.skill_tree.bind("<<TreeviewSelect>>", self.on_skill_select)

    def create_server_monitoring_tab(self):
        """创建服务器监控标签页"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="服务器监控")

        # 服务器状态
        status_frame = ttk.LabelFrame(monitor_frame, text="服务器状态")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 状态信息
        self.server_status_text = tk.Text(status_frame, height=8)
        self.server_status_text.pack(fill=tk.BOTH, expand=True)

        # 日志显示
        log_frame = ttk.LabelFrame(monitor_frame, text="服务器日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_frame, height=15)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 控制按钮
        control_frame = ttk.Frame(monitor_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="刷新状态", command=self.refresh_server_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="导出日志", command=self.export_log).pack(side=tk.LEFT, padx=5)

    def create_ip_management_tab(self):
        """创建IP管理标签页"""
        ip_frame = ttk.Frame(self.notebook)
        self.notebook.add(ip_frame, text="IP管理")

        # 封禁IP列表
        ban_frame = ttk.LabelFrame(ip_frame, text="封禁IP列表")
        ban_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        ban_columns = ("IP地址", "封禁时间", "封禁原因", "操作员")
        self.ban_tree = ttk.Treeview(ban_frame, columns=ban_columns, show="headings")

        for col in ban_columns:
            self.ban_tree.heading(col, text=col)
            self.ban_tree.column(col, width=120)

        ban_scrollbar = ttk.Scrollbar(ban_frame, orient=tk.VERTICAL, command=self.ban_tree.yview)
        self.ban_tree.configure(yscrollcommand=ban_scrollbar.set)

        self.ban_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ban_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # IP操作
        ip_control_frame = ttk.LabelFrame(ip_frame, text="IP操作")
        ip_control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)

        # 添加封禁
        ttk.Label(ip_control_frame, text="IP地址:").pack(anchor=tk.W)
        self.ban_ip_var = tk.StringVar()
        ttk.Entry(ip_control_frame, textvariable=self.ban_ip_var).pack(fill=tk.X, pady=2)

        ttk.Label(ip_control_frame, text="封禁原因:").pack(anchor=tk.W)
        self.ban_reason_var = tk.StringVar()
        ttk.Entry(ip_control_frame, textvariable=self.ban_reason_var).pack(fill=tk.X, pady=2)

        # 操作按钮
        ip_btn_frame = ttk.Frame(ip_control_frame)
        ip_btn_frame.pack(fill=tk.X, pady=10)

        ttk.Button(ip_btn_frame, text="封禁IP", command=self.ban_ip).pack(fill=tk.X, pady=2)
        ttk.Button(ip_btn_frame, text="解封IP", command=self.unban_ip).pack(fill=tk.X, pady=2)
        ttk.Button(ip_btn_frame, text="刷新列表", command=self.refresh_ban_list).pack(fill=tk.X, pady=2)

        # 绑定选择事件
        self.ban_tree.bind("<<TreeviewSelect>>", self.on_ban_select)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)

        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)

        # 更新时间
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # ==================== 服务器连接管理 ====================

    def connect_server(self):
        """连接到服务器"""
        try:
            # 获取服务器地址
            server_url = simpledialog.askstring("连接服务器", "请输入服务器地址:",
                                               initialvalue=self.server_url)
            if not server_url:
                return

            self.server_url = server_url

            # 先测试服务器是否在线
            response = requests.get(f"{self.server_url}/", timeout=5)
            if response.status_code != 200:
                messagebox.showerror("错误", "服务器无法访问")
                return

            # 管理员认证
            admin_password = simpledialog.askstring("管理员认证", "请输入管理员密码:", show='*')
            if not admin_password:
                return

            response = requests.post(f"{self.server_url}/admin/login",
                                   json={"password": admin_password},
                                   timeout=5)

            if response.status_code == 200:
                data = response.json()
                self.admin_token = data.get("token")
                self.connection_label.config(text="已连接", foreground="green")
                self.status_label.config(text="服务器连接成功")
                self.refresh_all()
                messagebox.showinfo("成功", "服务器连接成功！")
            elif response.status_code == 404:
                messagebox.showerror("错误", "服务器不支持管理员功能，请升级服务器版本")
            elif response.status_code == 401:
                messagebox.showerror("错误", "管理员密码错误")
            else:
                messagebox.showerror("错误", f"连接失败: {response.status_code}")

        except requests.exceptions.RequestException as e:
            messagebox.showerror("连接错误", f"无法连接到管理员认证服务: {e}")
            self.connection_label.config(text="连接失败", foreground="red")
        except Exception as e:
            messagebox.showerror("连接错误", f"连接过程中发生错误: {e}")
            self.connection_label.config(text="连接失败", foreground="red")

    def disconnect_server(self):
        """断开服务器连接"""
        self.admin_token = None
        self.connection_label.config(text="未连接", foreground="red")
        self.status_label.config(text="已断开服务器连接")

        # 清空所有列表
        for tree in [self.player_tree, self.monster_tree, self.equipment_tree,
                    self.map_tree, self.skill_tree, self.ban_tree]:
            for item in tree.get_children():
                tree.delete(item)

    def start_monitoring(self):
        """启动监控线程"""
        def monitor_loop():
            while True:
                if self.admin_token:
                    try:
                        self.refresh_player_list()
                        self.refresh_server_status()
                    except:
                        pass
                time.sleep(5)  # 每5秒刷新一次

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    # ==================== 玩家管理功能 ====================

    def refresh_player_list(self):
        """刷新玩家列表 - 显示游戏存档中的所有玩家"""
        if not self.admin_token:
            return

        try:
            # 使用管理员认证获取游戏存档数据
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.get(f"{self.server_url}/admin/game_saves", headers=headers)

            if response.status_code == 200:
                game_saves = response.json()

                # 清空现有列表
                for item in self.player_tree.get_children():
                    self.player_tree.delete(item)

                # 添加存档中的玩家数据
                for save in game_saves:
                    # 格式化保存时间
                    save_time = save.get("save_time", "")
                    if save_time:
                        try:
                            import datetime
                            if isinstance(save_time, (int, float)):
                                save_time = datetime.datetime.fromtimestamp(save_time).strftime("%m-%d %H:%M")
                            else:
                                save_time = str(save_time)[:16]  # 截取前16个字符
                        except:
                            save_time = "未知时间"
                    
                    self.player_tree.insert("", "end", values=(
                        save.get("player_id", ""),
                        save.get("username", "存档用户"),    # 账号
                        save.get("name", "未知角色"),         # 角色名
                        save.get("level", "1"),
                        save.get("job", "战士"),
                        save.get("location", "比奇省"),
                        f"存档({save_time})",                 # 显示存档时间而不是在线状态
                        save.get("ip", "存档数据")
                    ))
                
                # 显示统计信息
                self.status_label.config(text=f"已加载 {len(game_saves)} 个玩家存档")
            else:
                print(f"获取游戏存档失败: {response.status_code}")
                messagebox.showerror("错误", f"获取游戏存档失败: {response.status_code}")

        except Exception as e:
            print(f"刷新玩家列表失败: {e}")
            messagebox.showerror("错误", f"获取玩家列表失败: {e}")



    def refresh_all(self):
        """刷新所有数据"""
        self.refresh_player_list()
        self.refresh_monster_list()
        self.refresh_equipment_list()
        self.refresh_map_list()
        self.refresh_skill_list()
        self.refresh_ban_list()
        self.status_label.config(text="数据已刷新")

    def refresh_map_list(self):
        """刷新地图列表"""
        try:
            with open(self.get_resource_path("data/game/maps.json"), "r", encoding="utf-8") as f:
                data = json.load(f)

            # 清空现有列表
            for item in self.map_tree.get_children():
                self.map_tree.delete(item)

            # 获取地图数据
            maps = data.get("maps", {}) if isinstance(data, dict) else {}

            # 添加地图数据
            for map_name, map_data in maps.items():
                if isinstance(map_data, dict):
                    monster_count = len(map_data.get("monsters", []))
                    self.map_tree.insert("", "end", values=(
                        map_data.get("name", map_name),
                        map_data.get("description", ""),
                        map_data.get("level_requirement", "无限制"),
                        monster_count
                    ))

        except Exception as e:
            print(f"加载地图数据失败: {e}")

    def on_map_select(self, event):
        """地图选择事件"""
        selection = self.map_tree.selection()
        if not selection:
            return

        item = self.map_tree.item(selection[0])
        map_display_name = item['values'][0]

        try:
            with open(self.get_resource_path("data/game/maps.json"), "r", encoding="utf-8") as f:
                data = json.load(f)

            maps = data.get("maps", {}) if isinstance(data, dict) else {}

            # 查找对应的地图数据
            map_data = None
            map_key = None
            for key, value in maps.items():
                if isinstance(value, dict) and value.get("name") == map_display_name:
                    map_data = value
                    map_key = key
                    break

            if map_data:
                self.map_name_var.set(map_data.get("name", map_key))
                self.map_desc_var.set(map_data.get("description", ""))

        except Exception as e:
            print(f"加载地图数据失败: {e}")

    def save_map_data(self):
        """保存地图数据"""
        try:
            with open(self.get_resource_path("data/game/maps.json"), "r", encoding="utf-8") as f:
                maps = json.load(f)

            map_name = self.map_name_var.get()
            if not map_name:
                messagebox.showwarning("警告", "请输入地图名称")
                return

            if map_name in maps:
                maps[map_name]["description"] = self.map_desc_var.get()

                with open(self.get_resource_path("data/game/maps.json"), "w", encoding="utf-8") as f:
                    json.dump(maps, f, ensure_ascii=False, indent=2)

                self.refresh_map_list()
                messagebox.showinfo("成功", "地图数据已保存")
            else:
                messagebox.showwarning("警告", "地图不存在")

        except Exception as e:
            messagebox.showerror("错误", f"保存地图数据失败: {e}")

    def refresh_skill_list(self):
        """刷新技能列表"""
        try:
            with open(self.get_resource_path("data/game/skills.json"), "r", encoding="utf-8") as f:
                skills = json.load(f)

            # 清空现有列表
            for item in self.skill_tree.get_children():
                self.skill_tree.delete(item)

            # 添加技能数据
            for skill_name, skill_data in skills.items():
                self.skill_tree.insert("", "end", values=(
                    skill_name,
                    skill_data.get("type", ""),
                    skill_data.get("level", ""),
                    skill_data.get("damage", ""),
                    skill_data.get("mp_cost", "")
                ))

        except Exception as e:
            print(f"加载技能数据失败: {e}")

    def on_skill_select(self, event):
        """技能选择事件"""
        selection = self.skill_tree.selection()
        if not selection:
            return

        item = self.skill_tree.item(selection[0])
        skill_name = item['values'][0]
        self.skill_name_var.set(skill_name)

    def save_skill_data(self):
        """保存技能数据"""
        try:
            if not self.admin_token:
                messagebox.showwarning("警告", "请先连接到服务器")
                return

            # 获取当前技能数据
            with open(self.get_resource_path("data/game/skills.json"), "r", encoding="utf-8") as f:
                skills_data = json.load(f)

            skill_name = self.skill_name_var.get().strip()
            if not skill_name:
                messagebox.showwarning("警告", "请输入技能名称")
                return

            # 如果是新技能，添加基础结构
            if skill_name not in skills_data:
                skills_data[skill_name] = {
                    "type": "攻击技能",
                    "level": 1,
                    "damage": 10,
                    "mp_cost": 5,
                    "description": "新技能"
                }
                messagebox.showinfo("成功", f"已添加新技能: {skill_name}")
            else:
                messagebox.showinfo("提示", f"技能 {skill_name} 已存在，可在列表中修改属性")

            # 保存技能数据到本地
            with open(self.get_resource_path("data/game/skills.json"), "w", encoding="utf-8") as f:
                json.dump(skills_data, f, ensure_ascii=False, indent=2)

            # 同步到服务器
            if self.admin_token:
                try:
                    headers = {"Authorization": f"Bearer {self.admin_token}"}
                    response = requests.post(f"{self.server_url}/admin/data/skills",
                                           json={"skills": skills_data}, headers=headers)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("success"):
                            messagebox.showinfo("成功", "技能数据已保存到服务器")
                        else:
                            messagebox.showwarning("警告", f"服务器保存失败: {result.get('message', '未知错误')}")
                    else:
                        messagebox.showwarning("警告", "无法同步到服务器，仅保存到本地")
                except Exception as e:
                    print(f"同步到服务器失败: {e}")
                    messagebox.showwarning("警告", "无法同步到服务器，仅保存到本地")

            self.refresh_skill_list()

        except Exception as e:
            messagebox.showerror("错误", f"保存技能数据失败: {e}")

    # ==================== IP管理功能 ====================

    def refresh_ban_list(self):
        """刷新封禁IP列表"""
        if not self.admin_token:
            return

        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.get(f"{self.server_url}/admin/banned_ips", headers=headers)

            if response.status_code == 200:
                banned_ips = response.json()

                # 清空现有列表
                for item in self.ban_tree.get_children():
                    self.ban_tree.delete(item)

                # 添加封禁IP数据
                for ban_info in banned_ips:
                    self.ban_tree.insert("", "end", values=(
                        ban_info.get("ip", ""),
                        ban_info.get("ban_time", ""),
                        ban_info.get("reason", ""),
                        ban_info.get("admin", "")
                    ))

        except Exception as e:
            print(f"刷新封禁列表失败: {e}")

    def on_ban_select(self, event):
        """封禁选择事件"""
        selection = self.ban_tree.selection()
        if not selection:
            return

        item = self.ban_tree.item(selection[0])
        ip = item['values'][0]
        self.ban_ip_var.set(ip)

    def ban_ip(self):
        """封禁IP"""
        ip = self.ban_ip_var.get()
        reason = self.ban_reason_var.get()

        if not ip:
            messagebox.showwarning("警告", "请输入IP地址")
            return

        if not reason:
            reason = "管理员封禁"

        self.send_admin_command("ban_ip", {"ip": ip, "reason": reason})

    def unban_ip(self):
        """解封IP"""
        selection = self.ban_tree.selection()
        if not selection:
            ip = self.ban_ip_var.get()
            if not ip:
                messagebox.showwarning("警告", "请选择或输入要解封的IP")
                return
        else:
            item = self.ban_tree.item(selection[0])
            ip = item['values'][0]

        self.send_admin_command("unban_ip", {"ip": ip})

    # ==================== 其他功能 ====================

    def restart_server(self):
        """重启服务器"""
        if messagebox.askyesno("确认", "确定要重启服务器吗？这将断开所有玩家连接。"):
            self.send_admin_command("restart_server", {})

    def shutdown_server(self):
        """关闭服务器"""
        if messagebox.askyesno("确认", "确定要关闭服务器吗？这将断开所有玩家连接并停止服务器。"):
            self.send_admin_command("shutdown_server", {})

    def refresh_server_status(self):
        """刷新服务器状态"""
        if not self.admin_token:
            return

        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.get(f"{self.server_url}/admin/status", headers=headers)

            if response.status_code == 200:
                status = response.json()

                status_text = f"""服务器状态: {status.get('status', '未知')}
在线玩家数: {status.get('online_players', 0)}
服务器启动时间: {status.get('start_time', '未知')}
CPU使用率: {status.get('cpu_usage', 0)}%
内存使用: {status.get('memory_usage', 0)}MB
"""

                self.server_status_text.delete(1.0, tk.END)
                self.server_status_text.insert(1.0, status_text)

                self.server_info_label.config(text=f"在线: {status.get('online_players', 0)} 人")

        except Exception as e:
            print(f"获取服务器状态失败: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def export_log(self):
        """导出日志"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", "日志已导出")
            except Exception as e:
                messagebox.showerror("错误", f"导出日志失败: {e}")

    def backup_data(self):
        """备份数据"""
        try:
            from datetime import datetime
            import shutil
            
            # 选择备份目录
            backup_dir = filedialog.askdirectory(title="选择备份保存位置")
            if not backup_dir:
                return
            
            # 创建备份文件夹（带时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_folder = os.path.join(backup_dir, f"game_backup_{timestamp}")
            os.makedirs(backup_folder, exist_ok=True)
            
            # 要备份的文件列表
            backup_files = [
                self.get_resource_path("data/game/monsters.json"),
                self.get_resource_path("data/game/equipment.json"), 
                self.get_resource_path("data/game/maps.json"),
                self.get_resource_path("data/game/skills.json"),
                self.get_resource_path("data/game/drop_rates.json"),
                "game_database.db",
                "users.db"
            ]
            
            backup_count = 0
            failed_files = []
            
            # 备份每个文件
            for filename in backup_files:
                try:
                    if os.path.exists(filename):
                        shutil.copy2(filename, backup_folder)
                        backup_count += 1
                    else:
                        failed_files.append(f"{filename} (文件不存在)")
                except Exception as e:
                    failed_files.append(f"{filename} ({str(e)})")
            
            # 创建备份信息文件
            backup_info = {
                "backup_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "backup_files": backup_files,
                "successful_count": backup_count,
                "failed_files": failed_files,
                "server_url": self.server_url,
                "admin_connected": bool(self.admin_token)
            }
            
            info_file = os.path.join(backup_folder, "backup_info.json")
            with open(info_file, "w", encoding="utf-8") as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            # 显示结果
            if failed_files:
                result_msg = f"备份完成！\n成功: {backup_count} 个文件\n失败: {len(failed_files)} 个文件\n\n失败文件:\n" + "\n".join(failed_files)
                messagebox.showwarning("备份完成（有警告）", result_msg)
            else:
                messagebox.showinfo("成功", f"数据备份完成！\n备份位置: {backup_folder}\n备份文件: {backup_count} 个")
            
            self.status_label.config(text=f"数据已备份到: {backup_folder}")
            
        except Exception as e:
            messagebox.showerror("错误", f"备份失败: {e}")

    def restore_data(self):
        """恢复数据"""
        try:
            import shutil
            
            # 选择备份文件夹
            backup_folder = filedialog.askdirectory(title="选择要恢复的备份文件夹")
            if not backup_folder:
                return
            
            # 检查是否是有效的备份文件夹
            backup_info_file = os.path.join(backup_folder, "backup_info.json")
            if not os.path.exists(backup_info_file):
                messagebox.showerror("错误", "所选文件夹不是有效的备份文件夹")
                return
            
            # 读取备份信息
            with open(backup_info_file, "r", encoding="utf-8") as f:
                backup_info = json.load(f)
            
            # 显示备份信息并确认
            backup_time = backup_info.get("backup_time", "未知")
            file_count = backup_info.get("successful_count", 0)
            
            confirm_msg = f"确定要恢复此备份吗？\n\n备份时间: {backup_time}\n备份文件数: {file_count}\n\n⚠️ 警告: 这将覆盖当前的游戏数据！"
            
            if not messagebox.askyesno("确认恢复", confirm_msg):
                return
            
            # 创建当前数据的临时备份
            temp_backup_folder = f"temp_backup_{int(time.time())}"
            os.makedirs(temp_backup_folder, exist_ok=True)
            
            restore_files = backup_info.get("backup_files", [])
            restored_count = 0
            failed_files = []
            
            # 恢复每个文件
            for filename in restore_files:
                backup_file = os.path.join(backup_folder, filename)
                if os.path.exists(backup_file):
                    try:
                        # 备份当前文件（如果存在）
                        if os.path.exists(filename):
                            shutil.copy2(filename, temp_backup_folder)
                        
                        # 恢复文件
                        shutil.copy2(backup_file, filename)
                        restored_count += 1
                        
                    except Exception as e:
                        failed_files.append(f"{filename} ({str(e)})")
                else:
                    failed_files.append(f"{filename} (备份中不存在)")
            
            # 显示结果
            if failed_files:
                result_msg = f"恢复完成！\n成功: {restored_count} 个文件\n失败: {len(failed_files)} 个文件\n\n失败文件:\n" + "\n".join(failed_files)
                result_msg += f"\n\n当前数据已备份到: {temp_backup_folder}"
                messagebox.showwarning("恢复完成（有警告）", result_msg)
            else:
                messagebox.showinfo("成功", f"数据恢复完成！\n恢复文件: {restored_count} 个\n当前数据已备份到: {temp_backup_folder}")
            
            # 刷新所有数据
            self.refresh_all()
            self.status_label.config(text=f"数据已从备份恢复 ({backup_time})")
            
        except Exception as e:
            messagebox.showerror("错误", f"恢复失败: {e}")

    def clean_data(self):
        """清理数据"""
        try:
            # 创建清理选项窗口
            clean_window = tk.Toplevel(self.root)
            clean_window.title("数据清理")
            clean_window.geometry("400x500")
            clean_window.resizable(False, False)
            
            # 使窗口模态
            clean_window.transient(self.root)
            clean_window.grab_set()
            
            main_frame = ttk.Frame(clean_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 标题
            title_label = ttk.Label(main_frame, text="选择要清理的数据类型", font=("Arial", 12, "bold"))
            title_label.pack(pady=(0, 10))
            
            # 清理选项
            clean_options = {}
            options = [
                ("clean_logs", "清理服务器日志文件", "删除*.log文件"),
                ("clean_temp", "清理临时文件", "删除临时备份和缓存"),
                ("clean_offline_players", "清理离线玩家数据", "删除长期未登录玩家"),
                ("clean_empty_data", "清理空数据", "删除空的数据记录"),
                ("clean_old_backups", "清理旧备份", "删除7天前的自动备份"),
                ("reset_banned_ips", "重置IP封禁", "清空所有IP封禁记录"),
                ("clean_chat_history", "清理聊天记录", "删除聊天历史记录")
            ]
            
            for option_key, option_text, option_desc in options:
                frame = ttk.Frame(main_frame)
                frame.pack(fill=tk.X, pady=5)
                
                var = tk.BooleanVar()
                clean_options[option_key] = var
                
                cb = ttk.Checkbutton(frame, text=option_text, variable=var)
                cb.pack(anchor=tk.W)
                
                desc_label = ttk.Label(frame, text=f"  {option_desc}", foreground="gray")
                desc_label.pack(anchor=tk.W)
            
            # 警告信息
            warning_frame = ttk.LabelFrame(main_frame, text="⚠️ 警告")
            warning_frame.pack(fill=tk.X, pady=10)
            
            warning_text = tk.Text(warning_frame, height=4, wrap=tk.WORD)
            warning_text.insert(1.0, "数据清理操作不可逆！\n建议在清理前先进行数据备份。\n某些清理操作可能需要重启服务器才能生效。")
            warning_text.config(state=tk.DISABLED)
            warning_text.pack(fill=tk.X, padx=5, pady=5)
            
            # 按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=10)
            
            def perform_clean():
                """执行清理操作"""
                selected_options = [key for key, var in clean_options.items() if var.get()]
                
                if not selected_options:
                    messagebox.showwarning("警告", "请至少选择一个清理选项")
                    return
                
                confirm_msg = f"确定要执行以下清理操作吗？\n\n" + "\n".join([
                    next(opt[1] for opt in options if opt[0] == key) for key in selected_options
                ])
                
                if not messagebox.askyesno("确认清理", confirm_msg):
                    return
                
                cleaned_count = 0
                failed_operations = []
                
                # 执行清理操作
                for option in selected_options:
                    try:
                        if option == "clean_logs":
                            # 清理日志文件
                            log_files = [f for f in os.listdir(".") if f.endswith('.log')]
                            for log_file in log_files:
                                os.remove(log_file)
                            cleaned_count += len(log_files)
                            
                        elif option == "clean_temp":
                            # 清理临时文件
                            temp_folders = [f for f in os.listdir(".") if f.startswith('temp_backup_')]
                            for temp_folder in temp_folders:
                                if os.path.isdir(temp_folder):
                                    import shutil
                                    shutil.rmtree(temp_folder)
                            cleaned_count += len(temp_folders)
                            
                        elif option == "clean_offline_players":
                            # 清理离线玩家数据（这里只是示例，实际需要根据数据库结构调整）
                            messagebox.showinfo("提示", "离线玩家清理功能需要服务器配合，已发送清理命令")
                            
                        elif option == "clean_empty_data":
                            # 清理空数据记录
                            data_files = [self.get_resource_path("data/game/monsters.json"), self.get_resource_path("data/game/equipment.json"), self.get_resource_path("data/game/maps.json"), self.get_resource_path("data/game/skills.json")]
                            for data_file in data_files:
                                if os.path.exists(data_file):
                                    with open(data_file, "r", encoding="utf-8") as f:
                                        data = json.load(f)
                                    
                                    # 删除空记录
                                    if isinstance(data, dict):
                                        cleaned_data = {k: v for k, v in data.items() if v}
                                        if len(cleaned_data) != len(data):
                                            with open(data_file, "w", encoding="utf-8") as f:
                                                json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
                                            cleaned_count += 1
                            
                        elif option == "clean_old_backups":
                            # 清理旧备份（7天前）
                            import glob
                            from datetime import datetime, timedelta
                            
                            cutoff_date = datetime.now() - timedelta(days=7)
                            backup_folders = glob.glob("**/game_backup_*", recursive=True)
                            
                            for backup_folder in backup_folders:
                                try:
                                    folder_time = os.path.getctime(backup_folder)
                                    if datetime.fromtimestamp(folder_time) < cutoff_date:
                                        import shutil
                                        shutil.rmtree(backup_folder)
                                        cleaned_count += 1
                                except:
                                    pass
                                    
                        elif option == "reset_banned_ips":
                            # 重置IP封禁
                            if self.admin_token:
                                # 发送清理命令到服务器
                                self.send_admin_command("clear_banned_ips", {})
                            cleaned_count += 1
                            
                        elif option == "clean_chat_history":
                            # 清理聊天记录
                            try:
                                if os.path.exists("game_database.db"):
                                    import sqlite3
                                    conn = sqlite3.connect("game_database.db")
                                    cursor = conn.cursor()
                                    cursor.execute("DELETE FROM chat_messages")
                                    conn.commit()
                                    conn.close()
                                    cleaned_count += 1
                            except Exception as e:
                                failed_operations.append(f"清理聊天记录: {e}")
                                
                    except Exception as e:
                        failed_operations.append(f"{option}: {e}")
                
                # 显示结果
                if failed_operations:
                    result_msg = f"清理完成！\n成功操作: {cleaned_count}\n失败操作: {len(failed_operations)}\n\n失败详情:\n" + "\n".join(failed_operations)
                    messagebox.showwarning("清理完成（有警告）", result_msg)
                else:
                    messagebox.showinfo("成功", f"数据清理完成！\n执行了 {cleaned_count} 个清理操作")
                
                clean_window.destroy()
                self.refresh_all()
                self.status_label.config(text="数据清理完成")
            
            ttk.Button(button_frame, text="开始清理", command=perform_clean).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=clean_window.destroy).pack(side=tk.RIGHT, padx=5)
            
            # 居中显示
            clean_window.update_idletasks()
            x = (clean_window.winfo_screenwidth() // 2) - (400 // 2)
            y = (clean_window.winfo_screenheight() // 2) - (500 // 2)
            clean_window.geometry(f"400x500+{x}+{y}")
            
        except Exception as e:
            messagebox.showerror("错误", f"打开清理窗口失败: {e}")

    def open_log_viewer(self):
        """打开日志查看器"""
        try:
            # 创建日志查看器窗口
            log_window = tk.Toplevel(self.root)
            log_window.title("日志查看器")
            log_window.geometry("800x600")
            
            main_frame = ttk.Frame(log_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 工具栏
            toolbar = ttk.Frame(main_frame)
            toolbar.pack(fill=tk.X, pady=(0, 5))
            
            # 日志文件选择
            ttk.Label(toolbar, text="日志文件:").pack(side=tk.LEFT, padx=5)
            
            log_file_var = tk.StringVar()
            log_file_combo = ttk.Combobox(toolbar, textvariable=log_file_var, width=30)
            log_file_combo.pack(side=tk.LEFT, padx=5)
            
            # 搜索日志文件
            log_files = []
            for file in os.listdir("."):
                if file.endswith('.log') or file.endswith('.txt'):
                    log_files.append(file)
            
            if not log_files:
                log_files = ["server.log", "error.log", "access.log"]  # 默认日志文件
            
            log_file_combo['values'] = log_files
            if log_files:
                log_file_combo.set(log_files[0])
            
            # 刷新按钮
            def refresh_log():
                """刷新日志内容"""
                try:
                    selected_file = log_file_var.get()
                    if selected_file and os.path.exists(selected_file):
                        with open(selected_file, "r", encoding="utf-8", errors="ignore") as f:
                            content = f.read()
                        
                        log_text.delete(1.0, tk.END)
                        log_text.insert(1.0, content)
                        
                        # 自动滚动到底部
                        log_text.see(tk.END)
                        
                        status_label.config(text=f"已加载: {selected_file} ({len(content)} 字符)")
                    else:
                        log_text.delete(1.0, tk.END)
                        log_text.insert(1.0, f"日志文件不存在: {selected_file}")
                        status_label.config(text="文件不存在")
                except Exception as e:
                    log_text.delete(1.0, tk.END)
                    log_text.insert(1.0, f"读取日志文件失败: {e}")
                    status_label.config(text="读取失败")
            
            ttk.Button(toolbar, text="刷新", command=refresh_log).pack(side=tk.LEFT, padx=5)
            
            # 搜索框
            ttk.Label(toolbar, text="搜索:").pack(side=tk.LEFT, padx=(20, 5))
            search_var = tk.StringVar()
            search_entry = ttk.Entry(toolbar, textvariable=search_var, width=20)
            search_entry.pack(side=tk.LEFT, padx=5)
            
            def search_log():
                """搜索日志内容"""
                search_text = search_var.get()
                if not search_text:
                    return
                
                # 清除之前的高亮
                log_text.tag_remove("search", 1.0, tk.END)
                
                # 搜索并高亮
                start = 1.0
                count = 0
                while True:
                    pos = log_text.search(search_text, start, tk.END)
                    if not pos:
                        break
                    
                    end = f"{pos}+{len(search_text)}c"
                    log_text.tag_add("search", pos, end)
                    start = end
                    count += 1
                
                # 设置高亮样式
                log_text.tag_config("search", background="yellow")
                
                if count > 0:
                    # 跳转到第一个匹配项
                    first_match = log_text.search(search_text, 1.0, tk.END)
                    log_text.see(first_match)
                    status_label.config(text=f"找到 {count} 个匹配项")
                else:
                    status_label.config(text="未找到匹配项")
            
            ttk.Button(toolbar, text="搜索", command=search_log).pack(side=tk.LEFT, padx=5)
            
            # 清空按钮
            def clear_log():
                """清空日志显示"""
                log_text.delete(1.0, tk.END)
                status_label.config(text="日志已清空")
            
            ttk.Button(toolbar, text="清空显示", command=clear_log).pack(side=tk.LEFT, padx=5)
            
            # 日志内容显示
            log_frame = ttk.Frame(main_frame)
            log_frame.pack(fill=tk.BOTH, expand=True)
            
            log_text = tk.Text(log_frame, wrap=tk.WORD, font=("Consolas", 9))
            log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
            log_text.configure(yscrollcommand=log_scrollbar.set)
            
            log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 状态栏
            status_label = ttk.Label(main_frame, text="准备就绪")
            status_label.pack(fill=tk.X, pady=(5, 0))
            
            # 绑定事件
            search_entry.bind("<Return>", lambda e: search_log())
            log_file_combo.bind("<<ComboboxSelected>>", lambda e: refresh_log())
            
            # 初始加载
            refresh_log()
            
            # 自动刷新选项
            auto_refresh_var = tk.BooleanVar()
            auto_refresh_cb = ttk.Checkbutton(toolbar, text="自动刷新", variable=auto_refresh_var)
            auto_refresh_cb.pack(side=tk.RIGHT, padx=5)
            
            def auto_refresh():
                """自动刷新功能"""
                if auto_refresh_var.get():
                    refresh_log()
                log_window.after(5000, auto_refresh)  # 每5秒刷新一次
            
            auto_refresh()
            
        except Exception as e:
            messagebox.showerror("错误", f"打开日志查看器失败: {e}")

    def open_db_browser(self):
        """打开数据库浏览器"""
        try:
            import sqlite3
            
            # 创建数据库浏览器窗口
            db_window = tk.Toplevel(self.root)
            db_window.title("数据库浏览器")
            db_window.geometry("900x700")
            
            main_frame = ttk.Frame(db_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 工具栏
            toolbar = ttk.Frame(main_frame)
            toolbar.pack(fill=tk.X, pady=(0, 5))
            
            # 数据库文件选择
            ttk.Label(toolbar, text="数据库:").pack(side=tk.LEFT, padx=5)
            
            db_file_var = tk.StringVar()
            db_file_combo = ttk.Combobox(toolbar, textvariable=db_file_var, width=30)
            db_file_combo.pack(side=tk.LEFT, padx=5)
            
            # 搜索数据库文件
            db_files = [f for f in os.listdir(".") if f.endswith('.db')]
            if not db_files:
                db_files = ["game_database.db", "users.db"]
            
            db_file_combo['values'] = db_files
            if db_files:
                db_file_combo.set(db_files[0])
            
            # 表选择
            ttk.Label(toolbar, text="表:").pack(side=tk.LEFT, padx=(20, 5))
            
            table_var = tk.StringVar()
            table_combo = ttk.Combobox(toolbar, textvariable=table_var, width=20)
            table_combo.pack(side=tk.LEFT, padx=5)
            
            def load_tables():
                """加载数据库表"""
                try:
                    db_file = db_file_var.get()
                    if db_file and os.path.exists(db_file):
                        conn = sqlite3.connect(db_file)
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                        tables = [row[0] for row in cursor.fetchall()]
                        conn.close()
                        
                        table_combo['values'] = tables
                        if tables:
                            table_combo.set(tables[0])
                        
                        status_label.config(text=f"已连接到 {db_file}，找到 {len(tables)} 个表")
                    else:
                        table_combo['values'] = []
                        status_label.config(text="数据库文件不存在")
                except Exception as e:
                    table_combo['values'] = []
                    status_label.config(text=f"加载表失败: {e}")
            
            ttk.Button(toolbar, text="连接", command=load_tables).pack(side=tk.LEFT, padx=5)
            
            def load_table_data():
                """加载表数据"""
                try:
                    db_file = db_file_var.get()
                    table_name = table_var.get()
                    
                    if not db_file or not table_name:
                        return
                    
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    
                    # 获取表结构
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [row[1] for row in cursor.fetchall()]
                    
                    # 清空现有数据
                    for item in data_tree.get_children():
                        data_tree.delete(item)
                    
                    # 设置列
                    data_tree['columns'] = columns
                    data_tree['show'] = 'headings'
                    
                    for col in columns:
                        data_tree.heading(col, text=col)
                        data_tree.column(col, width=100)
                    
                    # 获取数据
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 1000")  # 限制1000行
                    rows = cursor.fetchall()
                    
                    # 插入数据
                    for row in rows:
                        data_tree.insert('', 'end', values=row)
                    
                    conn.close()
                    status_label.config(text=f"已加载表 {table_name}，共 {len(rows)} 行数据")
                    
                except Exception as e:
                    status_label.config(text=f"加载数据失败: {e}")
            
            ttk.Button(toolbar, text="查询", command=load_table_data).pack(side=tk.LEFT, padx=5)
            
            # SQL查询框
            ttk.Label(toolbar, text="SQL:").pack(side=tk.LEFT, padx=(20, 5))
            sql_var = tk.StringVar()
            sql_entry = ttk.Entry(toolbar, textvariable=sql_var, width=30)
            sql_entry.pack(side=tk.LEFT, padx=5)
            
            def execute_sql():
                """执行SQL查询"""
                try:
                    db_file = db_file_var.get()
                    sql = sql_var.get().strip()
                    
                    if not db_file or not sql:
                        return
                    
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    cursor.execute(sql)
                    
                    if sql.upper().startswith('SELECT'):
                        # 查询操作
                        rows = cursor.fetchall()
                        columns = [description[0] for description in cursor.description]
                        
                        # 清空现有数据
                        for item in data_tree.get_children():
                            data_tree.delete(item)
                        
                        # 设置列
                        data_tree['columns'] = columns
                        data_tree['show'] = 'headings'
                        
                        for col in columns:
                            data_tree.heading(col, text=col)
                            data_tree.column(col, width=100)
                        
                        # 插入数据
                        for row in rows:
                            data_tree.insert('', 'end', values=row)
                        
                        status_label.config(text=f"查询完成，返回 {len(rows)} 行")
                    else:
                        # 修改操作
                        conn.commit()
                        status_label.config(text=f"SQL执行完成，影响 {cursor.rowcount} 行")
                    
                    conn.close()
                    
                except Exception as e:
                    status_label.config(text=f"SQL执行失败: {e}")
            
            ttk.Button(toolbar, text="执行SQL", command=execute_sql).pack(side=tk.LEFT, padx=5)
            
            # 分割窗口
            paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
            paned_window.pack(fill=tk.BOTH, expand=True)
            
            # 数据显示区域
            data_frame = ttk.Frame(paned_window)
            paned_window.add(data_frame, weight=3)
            
            data_tree = ttk.Treeview(data_frame)
            data_scrollbar_v = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=data_tree.yview)
            data_scrollbar_h = ttk.Scrollbar(data_frame, orient=tk.HORIZONTAL, command=data_tree.xview)
            data_tree.configure(yscrollcommand=data_scrollbar_v.set, xscrollcommand=data_scrollbar_h.set)
            
            data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            data_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
            data_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
            
            # SQL编辑器
            sql_frame = ttk.LabelFrame(paned_window, text="SQL编辑器")
            paned_window.add(sql_frame, weight=1)
            
            sql_text = tk.Text(sql_frame, height=8, font=("Consolas", 10))
            sql_text_scrollbar = ttk.Scrollbar(sql_frame, orient=tk.VERTICAL, command=sql_text.yview)
            sql_text.configure(yscrollcommand=sql_text_scrollbar.set)
            
            sql_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            sql_text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 预设SQL模板
            sql_templates = [
                "SELECT * FROM users WHERE username LIKE '%test%'",
                "SELECT COUNT(*) FROM players WHERE is_online = 1",
                "SELECT * FROM chat_messages ORDER BY timestamp DESC LIMIT 50",
                "DELETE FROM players WHERE last_active < date('now', '-30 days')"
            ]
            
            sql_text.insert(1.0, "-- SQL查询编辑器\n-- 常用查询模板:\n\n")
            for template in sql_templates:
                sql_text.insert(tk.END, f"-- {template}\n")
            
            def execute_text_sql():
                """执行文本框中的SQL"""
                sql = sql_text.get("sel.first", "sel.last") if sql_text.tag_ranges("sel") else sql_text.get(1.0, tk.END)
                sql_var.set(sql.strip())
                execute_sql()
            
            # 按钮框架
            sql_button_frame = ttk.Frame(sql_frame)
            sql_button_frame.pack(fill=tk.X, pady=5)
            
            ttk.Button(sql_button_frame, text="执行选中SQL", command=execute_text_sql).pack(side=tk.LEFT, padx=5)
            ttk.Button(sql_button_frame, text="清空", command=lambda: sql_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=5)
            
            # 状态栏
            status_label = ttk.Label(main_frame, text="准备就绪")
            status_label.pack(fill=tk.X, pady=(5, 0))
            
            # 绑定事件
            sql_entry.bind("<Return>", lambda e: execute_sql())
            db_file_combo.bind("<<ComboboxSelected>>", lambda e: load_tables())
            table_combo.bind("<<ComboboxSelected>>", lambda e: load_table_data())
            
            # 初始加载
            load_tables()
            
        except Exception as e:
            messagebox.showerror("错误", f"打开数据库浏览器失败: {e}")

    def open_config_editor(self):
        """打开配置编辑器"""
        try:
            # 创建配置编辑器窗口
            config_window = tk.Toplevel(self.root)
            config_window.title("配置编辑器")
            config_window.geometry("800x600")
            
            main_frame = ttk.Frame(config_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 工具栏
            toolbar = ttk.Frame(main_frame)
            toolbar.pack(fill=tk.X, pady=(0, 5))
            
            # 配置文件选择
            ttk.Label(toolbar, text="配置文件:").pack(side=tk.LEFT, padx=5)
            
            config_file_var = tk.StringVar()
            config_file_combo = ttk.Combobox(toolbar, textvariable=config_file_var, width=30)
            config_file_combo.pack(side=tk.LEFT, padx=5)
            
            # 搜索配置文件
            config_files = []
            for file in os.listdir("."):
                if file.endswith(('.json', '.ini', '.cfg', '.conf', '.txt')) and 'config' in file.lower():
                    config_files.append(file)
            
            # 添加常见配置文件
            common_configs = ["server_config.json", "admin_config.json", "login_config.json"]
            for config in common_configs:
                if config not in config_files:
                    config_files.append(config)
            
            config_file_combo['values'] = config_files
            if config_files:
                config_file_combo.set(config_files[0])
            
            def load_config():
                """加载配置文件"""
                try:
                    config_file = config_file_var.get()
                    if config_file and os.path.exists(config_file):
                        with open(config_file, "r", encoding="utf-8") as f:
                            content = f.read()
                        
                        config_text.delete(1.0, tk.END)
                        config_text.insert(1.0, content)
                        
                        # 尝试解析JSON并显示结构化视图
                        if config_file.endswith('.json'):
                            try:
                                config_data = json.loads(content)
                                load_json_tree(config_data)
                            except:
                                pass
                        
                        status_label.config(text=f"已加载: {config_file}")
                    else:
                        config_text.delete(1.0, tk.END)
                        config_text.insert(1.0, f"# 新配置文件: {config_file}\n# 请添加配置内容")
                        status_label.config(text="新文件")
                except Exception as e:
                    config_text.delete(1.0, tk.END)
                    config_text.insert(1.0, f"# 加载配置文件失败: {e}")
                    status_label.config(text="加载失败")
            
            def save_config():
                """保存配置文件"""
                try:
                    config_file = config_file_var.get()
                    if not config_file:
                        return
                    
                    content = config_text.get(1.0, tk.END).rstrip()
                    
                    # 验证JSON格式
                    if config_file.endswith('.json'):
                        try:
                            json.loads(content)
                        except json.JSONDecodeError as e:
                            if not messagebox.askyesno("JSON格式错误", f"配置文件格式错误:\n{e}\n\n是否仍要保存？"):
                                return
                    
                    # 备份原文件
                    if os.path.exists(config_file):
                        backup_file = f"{config_file}.backup_{int(time.time())}"
                        import shutil
                        shutil.copy2(config_file, backup_file)
                    
                    # 保存文件
                    with open(config_file, "w", encoding="utf-8") as f:
                        f.write(content)
                    
                    status_label.config(text=f"已保存: {config_file}")
                    messagebox.showinfo("成功", "配置文件已保存")
                    
                except Exception as e:
                    messagebox.showerror("错误", f"保存配置文件失败: {e}")
            
            ttk.Button(toolbar, text="加载", command=load_config).pack(side=tk.LEFT, padx=5)
            ttk.Button(toolbar, text="保存", command=save_config).pack(side=tk.LEFT, padx=5)
            
            def validate_json():
                """验证JSON格式"""
                try:
                    content = config_text.get(1.0, tk.END).rstrip()
                    if content:
                        json.loads(content)
                        messagebox.showinfo("验证成功", "JSON格式正确")
                    else:
                        messagebox.showwarning("警告", "内容为空")
                except json.JSONDecodeError as e:
                    messagebox.showerror("JSON错误", f"格式错误:\n{e}")
                except Exception as e:
                    messagebox.showerror("错误", f"验证失败: {e}")
            
            ttk.Button(toolbar, text="验证JSON", command=validate_json).pack(side=tk.LEFT, padx=5)
            
            def format_json():
                """格式化JSON"""
                try:
                    content = config_text.get(1.0, tk.END).rstrip()
                    if content:
                        data = json.loads(content)
                        formatted = json.dumps(data, ensure_ascii=False, indent=2)
                        config_text.delete(1.0, tk.END)
                        config_text.insert(1.0, formatted)
                        status_label.config(text="JSON已格式化")
                except Exception as e:
                    messagebox.showerror("错误", f"格式化失败: {e}")
            
            ttk.Button(toolbar, text="格式化", command=format_json).pack(side=tk.LEFT, padx=5)
            
            # 分割窗口
            paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
            paned_window.pack(fill=tk.BOTH, expand=True)
            
            # 配置文本编辑器
            text_frame = ttk.LabelFrame(paned_window, text="配置内容")
            paned_window.add(text_frame, weight=2)
            
            config_text = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 10))
            text_scrollbar_v = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=config_text.yview)
            text_scrollbar_h = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=config_text.xview)
            config_text.configure(yscrollcommand=text_scrollbar_v.set, xscrollcommand=text_scrollbar_h.set)
            
            config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            text_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
            text_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
            
            # 结构化视图（用于JSON）
            tree_frame = ttk.LabelFrame(paned_window, text="结构视图")
            paned_window.add(tree_frame, weight=1)
            
            config_tree = ttk.Treeview(tree_frame)
            tree_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=config_tree.yview)
            config_tree.configure(yscrollcommand=tree_scrollbar.set)
            
            config_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            def load_json_tree(data, parent="", prefix=""):
                """加载JSON数据到树形视图"""
                config_tree.delete(*config_tree.get_children())
                
                def add_items(obj, parent_id):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            item_id = config_tree.insert(parent_id, 'end', text=key, values=[type(value).__name__])
                            if isinstance(value, (dict, list)):
                                add_items(value, item_id)
                            else:
                                config_tree.set(item_id, 'value', str(value))
                    elif isinstance(obj, list):
                        for i, value in enumerate(obj):
                            item_id = config_tree.insert(parent_id, 'end', text=f"[{i}]", values=[type(value).__name__])
                            if isinstance(value, (dict, list)):
                                add_items(value, item_id)
                            else:
                                config_tree.set(item_id, 'value', str(value))
                
                # 设置列
                config_tree['columns'] = ('type', 'value')
                config_tree.heading('#0', text='键/索引')
                config_tree.heading('type', text='类型')
                config_tree.heading('value', text='值')
                config_tree.column('type', width=80)
                config_tree.column('value', width=200)
                
                add_items(data, '')
            
            # 配置模板
            template_frame = ttk.LabelFrame(main_frame, text="配置模板")
            template_frame.pack(fill=tk.X, pady=5)
            
            template_buttons_frame = ttk.Frame(template_frame)
            template_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
            
            def load_template(template_name):
                """加载配置模板"""
                templates = {
                    "server_config": {
                        "server": {
                            "host": "0.0.0.0",
                            "port": 8000,
                            "debug": False
                        },
                        "database": {
                            "path": "game_database.db",
                            "backup_interval": 3600
                        },
                        "game": {
                            "max_players": 1000,
                            "save_interval": 300,
                            "enable_chat": True
                        }
                    },
                    "admin_config": {
                        "admin": {
                            "default_password": "admin123",
                            "session_timeout": 3600,
                            "allowed_ips": ["127.0.0.1"]
                        },
                        "logging": {
                            "level": "INFO",
                            "file": "admin.log"
                        }
                    },
                    "login_config": {
                        "auth": {
                            "enable_registration": True,
                            "require_email": False,
                            "password_min_length": 6
                        },
                        "security": {
                            "max_login_attempts": 5,
                            "lockout_duration": 300
                        }
                    }
                }
                
                if template_name in templates:
                    template_json = json.dumps(templates[template_name], ensure_ascii=False, indent=2)
                    config_text.delete(1.0, tk.END)
                    config_text.insert(1.0, template_json)
                    load_json_tree(templates[template_name])
            
            ttk.Button(template_buttons_frame, text="服务器配置", 
                      command=lambda: load_template("server_config")).pack(side=tk.LEFT, padx=5)
            ttk.Button(template_buttons_frame, text="管理员配置", 
                      command=lambda: load_template("admin_config")).pack(side=tk.LEFT, padx=5)
            ttk.Button(template_buttons_frame, text="登录配置", 
                      command=lambda: load_template("login_config")).pack(side=tk.LEFT, padx=5)
            
            # 状态栏
            status_label = ttk.Label(main_frame, text="准备就绪")
            status_label.pack(fill=tk.X, pady=(5, 0))
            
            # 绑定事件
            config_file_combo.bind("<<ComboboxSelected>>", lambda e: load_config())
            
            # 语法高亮（简单实现）
            def highlight_json():
                """简单的JSON语法高亮"""
                try:
                    content = config_text.get(1.0, tk.END)
                    # 这里可以添加更复杂的语法高亮逻辑
                    # 现在只是一个占位符
                except:
                    pass
            
            # 初始加载
            load_config()
            
        except Exception as e:
            messagebox.showerror("错误", f"打开配置编辑器失败: {e}")

    def refresh_equipment_list(self):
        """刷新装备列表"""
        try:
            with open(self.get_resource_path("data/game/equipment.json"), "r", encoding="utf-8") as f:
                equipment_data = json.load(f)

            # 清空现有列表
            for item in self.equipment_tree.get_children():
                self.equipment_tree.delete(item)

            # 添加装备数据
            for category, items in equipment_data.items():
                if isinstance(items, dict):
                    for item_name, item_data in items.items():
                        if isinstance(item_data, dict):
                            self.equipment_tree.insert("", "end", values=(
                                item_name,
                                category,
                                item_data.get("level", ""),
                                item_data.get("attack", item_data.get("attack_range", ["", ""])[0] if isinstance(item_data.get("attack_range"), list) else ""),
                                item_data.get("defense", item_data.get("defense_range", ["", ""])[0] if isinstance(item_data.get("defense_range"), list) else ""),
                                item_data.get("price", "")
                            ))

        except Exception as e:
            print(f"加载装备数据失败: {e}")

    def on_equipment_select(self, event):
        """装备选择事件"""
        selection = self.equipment_tree.selection()
        if not selection:
            return

        item = self.equipment_tree.item(selection[0])
        equipment_name = item['values'][0]
        equipment_type = item['values'][1]

        try:
            with open(self.get_resource_path("data/game/equipment.json"), "r", encoding="utf-8") as f:
                equipment_data = json.load(f)

            # 查找对应的装备数据
            if equipment_type in equipment_data and equipment_name in equipment_data[equipment_type]:
                item_data = equipment_data[equipment_type][equipment_name]
                
                self.eq_name_var.set(equipment_name)
                self.eq_type_var.set(equipment_type)
                self.eq_level_var.set(str(item_data.get("level", "")))

        except Exception as e:
            print(f"加载装备数据失败: {e}")

    def save_equipment_data(self):
        """保存装备数据"""
        try:
            with open(self.get_resource_path("data/game/equipment.json"), "r", encoding="utf-8") as f:
                equipment_data = json.load(f)

            equipment_name = self.eq_name_var.get()
            equipment_type = self.eq_type_var.get()
            equipment_level = self.eq_level_var.get()

            if not equipment_name or not equipment_type:
                messagebox.showwarning("警告", "请输入装备名称和类型")
                return

            # 更新装备数据
            if equipment_type in equipment_data and equipment_name in equipment_data[equipment_type]:
                equipment_data[equipment_type][equipment_name]["level"] = int(equipment_level) if equipment_level else 1

                with open(self.get_resource_path("data/game/equipment.json"), "w", encoding="utf-8") as f:
                    json.dump(equipment_data, f, ensure_ascii=False, indent=2)

                self.refresh_equipment_list()
                messagebox.showinfo("成功", "装备数据已保存")
            else:
                messagebox.showwarning("警告", "装备不存在")

        except Exception as e:
            messagebox.showerror("错误", f"保存装备数据失败: {e}")

    def add_equipment(self):
        """添加装备"""
        messagebox.showinfo("提示", "添加装备功能需要通过编辑equipment.json文件实现")

    def delete_equipment(self):
        """删除装备"""
        selection = self.equipment_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的装备")
            return

        item = self.equipment_tree.item(selection[0])
        equipment_name = item['values'][0]
        equipment_type = item['values'][1]

        if messagebox.askyesno("确认", f"确定要删除装备 '{equipment_name}' 吗？"):
            try:
                with open(self.get_resource_path("data/game/equipment.json"), "r", encoding="utf-8") as f:
                    equipment_data = json.load(f)

                if equipment_type in equipment_data and equipment_name in equipment_data[equipment_type]:
                    del equipment_data[equipment_type][equipment_name]

                    with open(self.get_resource_path("data/game/equipment.json"), "w", encoding="utf-8") as f:
                        json.dump(equipment_data, f, ensure_ascii=False, indent=2)

                    self.refresh_equipment_list()
                    messagebox.showinfo("成功", "装备已删除")
                else:
                    messagebox.showwarning("警告", "装备不存在")

            except Exception as e:
                messagebox.showerror("错误", f"删除装备失败: {e}")

    def open_player_editor(self, player_id):
        """打开玩家数据编辑器"""
        if not self.admin_token:
            messagebox.showwarning("警告", "请先连接到服务器")
            return

        # 创建编辑窗口
        editor_window = tk.Toplevel(self.root)
        editor_window.title(f"编辑玩家数据 - {player_id}")
        editor_window.geometry("400x600")
        editor_window.resizable(False, False)
        
        # 获取玩家当前数据
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.get(f"{self.server_url}/admin/player/{player_id}", headers=headers)
            
            if response.status_code != 200:
                messagebox.showerror("错误", "无法获取玩家数据")
                editor_window.destroy()
                return
                
            player_data = response.json()
        except Exception as e:
            messagebox.showerror("错误", f"获取玩家数据失败: {e}")
            editor_window.destroy()
            return

        # 创建表单
        main_frame = ttk.Frame(editor_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 玩家信息标题
        title_label = ttk.Label(main_frame, text=f"玩家: {player_data.get('name', '未知')}", 
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))

        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 编辑字段变量
        edit_vars = {}

                 # 支持编辑的字段配置
        fields_config = [
            ("name", "玩家姓名", str, player_data.get('name', '')),
            ("level", "等级", int, player_data.get('level', 1)),
            ("experience", "经验值", int, player_data.get('experience', 0)),
            ("gold", "金币", int, player_data.get('gold', 0)),
            ("hp", "当前生命值", int, player_data.get('hp', 100)),
            ("max_hp", "最大生命值", int, player_data.get('max_hp', 100)),
            ("mana", "当前魔法值", int, player_data.get('mp', 50)),  # 服务器使用mana字段
            ("max_mana", "最大魔法值", int, player_data.get('max_mp', 50))  # 服务器使用max_mana字段
        ]

        # 创建编辑字段
        for field_name, display_name, field_type, current_value in fields_config:
            # 字段框架
            field_frame = ttk.Frame(scrollable_frame)
            field_frame.pack(fill=tk.X, pady=5)
            
            # 标签
            label = ttk.Label(field_frame, text=f"{display_name}:", width=15, anchor="w")
            label.pack(side=tk.LEFT, padx=(0, 10))
            
            # 输入框
            var = tk.StringVar(value=str(current_value))
            edit_vars[field_name] = (var, field_type)
            
            entry = ttk.Entry(field_frame, textvariable=var, width=20)
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 只读信息显示
        info_frame = ttk.LabelFrame(scrollable_frame, text="只读信息")
        info_frame.pack(fill=tk.X, pady=10)

        readonly_info = [
            ("玩家ID", player_data.get('id', '')),
            ("职业", player_data.get('job', '')),
            ("位置", player_data.get('location', '')),
            ("IP地址", player_data.get('ip', '')),
            ("最后活动", player_data.get('last_active', ''))
        ]

        for label_text, value in readonly_info:
            info_row = ttk.Frame(info_frame)
            info_row.pack(fill=tk.X, pady=2)
            
            ttk.Label(info_row, text=f"{label_text}:", width=15, anchor="w").pack(side=tk.LEFT)
            ttk.Label(info_row, text=str(value), anchor="w").pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        def save_changes():
            """保存修改"""
            try:
                # 收集修改的数据
                update_data = {}
                
                for field_name, (var, field_type) in edit_vars.items():
                    try:
                        value = var.get().strip()
                        if value:
                            if field_type == int:
                                update_data[field_name] = int(value)
                            else:
                                update_data[field_name] = value
                    except ValueError:
                        messagebox.showerror("错误", f"{field_name} 字段格式不正确")
                        return

                if not update_data:
                    messagebox.showwarning("警告", "没有要保存的修改")
                    return

                # 发送更新请求
                headers = {"Authorization": f"Bearer {self.admin_token}"}
                response = requests.post(f"{self.server_url}/api/admin/update_player/{player_id}",
                                       json=update_data, headers=headers)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        messagebox.showinfo("成功", "玩家数据已保存")
                        editor_window.destroy()
                        self.refresh_player_list()  # 刷新玩家列表
                    else:
                        messagebox.showerror("错误", result.get("message", "保存失败"))
                else:
                    messagebox.showerror("错误", f"服务器错误: {response.status_code}")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

        def reset_values():
            """重置为原始值"""
            for field_name, display_name, field_type, current_value in fields_config:
                if field_name in edit_vars:
                    edit_vars[field_name][0].set(str(current_value))

        # 按钮
        ttk.Button(button_frame, text="保存修改", command=save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置", command=reset_values).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=editor_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 设置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        editor_window.bind("<MouseWheel>", on_mousewheel)
        
        # 使窗口模态
        editor_window.transient(self.root)
        editor_window.grab_set()
        
        # 居中显示
        editor_window.update_idletasks()
        x = (editor_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (editor_window.winfo_screenheight() // 2) - (600 // 2)
        editor_window.geometry(f"400x600+{x}+{y}")

    # ==================== 缺失的方法实现 ====================
    
    def kick_all_players(self):
        """踢出所有玩家"""
        if not self.admin_token:
            messagebox.showwarning("警告", "请先连接到服务器")
            return
            
        if messagebox.askyesno("确认", "确定要踢出所有在线玩家吗？"):
            self.send_admin_command("kick_all_players", {})
    
    def send_announcement(self):
        """发送服务器公告"""
        if not self.admin_token:
            messagebox.showwarning("警告", "请先连接到服务器")
            return
            
        message = simpledialog.askstring("服务器公告", "请输入公告内容:")
        if message:
            self.send_admin_command("server_announcement", {"message": message})
    
    def kick_player(self):
        """踢出选中的玩家"""
        selection = self.player_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要踢出的玩家")
            return
            
        item = self.player_tree.item(selection[0])
        player_id = item['values'][0]
        player_name = item['values'][2]  # 角色名
        
        if messagebox.askyesno("确认", f"确定要踢出玩家 '{player_name}' 吗？"):
            self.send_admin_command("kick_player", {"player_id": player_id})
    
    def ban_player(self):
        """封禁选中的玩家"""
        selection = self.player_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要封禁的玩家")
            return
            
        item = self.player_tree.item(selection[0])
        player_id = item['values'][0]
        player_name = item['values'][2]  # 角色名
        player_ip = item['values'][7] if len(item['values']) > 7 else ""
        
        reason = simpledialog.askstring("封禁原因", "请输入封禁原因:", initialvalue="违规行为")
        if reason:
            self.send_admin_command("ban_player", {"player_id": player_id, "reason": reason})
            if player_ip:
                self.send_admin_command("ban_ip", {"ip": player_ip, "reason": reason})
    
    def unban_player(self):
        """解封玩家"""
        player_name = simpledialog.askstring("解封玩家", "请输入要解封的玩家名称:")
        if player_name:
            self.send_admin_command("unban_player", {"player_name": player_name})
    
    def edit_player_data(self):
        """编辑玩家数据"""
        selection = self.player_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的玩家")
            return
            
        item = self.player_tree.item(selection[0])
        player_id = item['values'][0]
        
        if player_id and player_id != "N/A" and player_id != "ERROR":
            self.open_player_editor(player_id)
        else:
            messagebox.showwarning("警告", "无效的玩家ID")
    
    def send_message_to_player(self):
        """发送消息给玩家"""
        selection = self.player_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要发送消息的玩家")
            return
            
        item = self.player_tree.item(selection[0])
        player_id = item['values'][0]
        player_name = item['values'][2]  # 角色名
        
        message = simpledialog.askstring("发送消息", f"发送消息给 {player_name}:")
        if message:
            self.send_admin_command("send_message", {"player_id": player_id, "message": message})
    
    def teleport_player(self):
        """传送玩家"""
        selection = self.player_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要传送的玩家")
            return
            
        item = self.player_tree.item(selection[0])
        player_id = item['values'][0]
        player_name = item['values'][2]  # 角色名
        
        # 获取可用地图列表
        try:
            with open(self.get_resource_path("data/game/maps.json"), "r", encoding="utf-8") as f:
                maps_data = json.load(f)
            maps = list(maps_data.get("maps", {}).keys()) if isinstance(maps_data, dict) else []
        except:
            maps = ["比奇省", "盟重省", "白日门"]
        
        if maps:
            # 创建地图选择对话框
            map_choice = simpledialog.askstring("传送玩家", 
                f"将 {player_name} 传送到哪个地图？\n可选地图: {', '.join(maps)}")
            if map_choice and map_choice in maps:
                self.send_admin_command("teleport_player", {"player_id": player_id, "map": map_choice})
            elif map_choice:
                messagebox.showwarning("警告", "无效的地图名称")
        else:
            messagebox.showwarning("警告", "无法获取地图列表")
    
    def on_player_select(self, event):
        """玩家选择事件 - 显示存档详细信息"""
        selection = self.player_tree.selection()
        if not selection:
            self.player_info_text.delete(1.0, tk.END)
            return
            
        item = self.player_tree.item(selection[0])
        values = item['values']
        
        if len(values) >= 8:
            player_id = values[0]
            
            # 从存档数据获取详细信息
            detailed_info = self.get_save_detailed_info(player_id)
            
            if detailed_info:
                # 显示详细的存档信息
                save_time = detailed_info.get('save_time', '')
                if save_time:
                    try:
                        import datetime
                        if isinstance(save_time, (int, float)):
                            save_time = datetime.datetime.fromtimestamp(save_time).strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        save_time = str(save_time)
                
                player_info = f"""=== 存档基本信息 ===
存档ID: {detailed_info.get('player_id', values[0])}
角色名: {detailed_info.get('name', values[2])}
等级: {detailed_info.get('level', values[3])}
职业: {detailed_info.get('job', values[4])}
位置: {detailed_info.get('location', values[5])}
存档时间: {save_time}

=== 角色属性 ===
生命值: {detailed_info.get('hp', 0)}/{detailed_info.get('max_hp', 0)}
魔法值: {detailed_info.get('mp', 0)}/{detailed_info.get('max_mp', 0)}
金币: {detailed_info.get('gold', 0)}
经验值: {detailed_info.get('experience', 0)}

=== 装备信息 ==="""
                
                # 显示装备信息
                weapon = detailed_info.get('weapon', {})
                armor = detailed_info.get('armor', {})
                if weapon and weapon.get('name'):
                    player_info += f"\n武器: {weapon.get('name', '无')} (伤害: {weapon.get('damage_range', [0,0])})"
                if armor and armor.get('name'):
                    player_info += f"\n防具: {armor.get('name', '无')} (防御: {armor.get('defense_range', [0,0])})"
                
                # 显示背包信息
                inventory = detailed_info.get('inventory', [])
                player_info += f"\n\n=== 背包信息 ===\n背包物品数量: {len(inventory)}"
                if inventory:
                    for i, item in enumerate(inventory[:5]):  # 只显示前5个物品
                        item_name = item.get('name', '未知物品')
                        item_qty = item.get('quantity', item.get('count', 1))
                        player_info += f"\n  {i+1}. {item_name} x{item_qty}"
                    if len(inventory) > 5:
                        player_info += f"\n  ... 还有 {len(inventory)-5} 个物品"
                
                # 显示技能信息
                skill_levels = detailed_info.get('skill_levels', {})
                player_info += f"\n\n=== 技能信息 ===\n已学技能数量: {len(skill_levels)}"
                if skill_levels:
                    for skill, level in skill_levels.items():
                        if level > 0:
                            player_info += f"\n  {skill}: Lv.{level}"
                
                # 显示宠物信息
                pet = detailed_info.get('pet', {})
                if pet and pet.get('name'):
                    player_info += f"\n\n=== 宠物信息 ===\n宠物名: {pet.get('name', '无')}"
                    player_info += f"\n宠物等级: {pet.get('level', 1)}"
                    player_info += f"\n宠物血量: {pet.get('hp', 0)}/{pet.get('max_hp', 0)}"
                
            else:
                # 如果无法获取详细信息，显示基本信息
                player_info = f"""=== 基本信息 ===
存档ID: {values[0]}
账号: {values[1]}
角色名: {values[2]}
等级: {values[3]}
职业: {values[4]}
位置: {values[5]}
存档时间: {values[6]}
备注: {values[7]}

注意: 无法获取详细存档信息
请检查服务器连接状态"""
        else:
            player_info = "无法获取存档详细信息"
            
        self.player_info_text.delete(1.0, tk.END)
        self.player_info_text.insert(1.0, player_info)
    
    def get_save_detailed_info(self, player_id):
        """获取存档详细信息"""
        if not self.admin_token:
            return None
            
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.get(f"{self.server_url}/admin/game_saves", 
                                  headers=headers, timeout=5)
            if response.status_code == 200:
                game_saves = response.json()
                # 查找匹配的存档
                for save in game_saves:
                    if save.get('player_id') == player_id:
                        return save
                print(f"未找到存档ID: {player_id}")
                return None
            else:
                print(f"获取存档详细信息失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取存档详细信息异常: {e}")
            return None
    
    def get_player_detailed_info(self, player_id):
        """获取玩家详细信息"""
        if not self.admin_token:
            return None
            
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.get(f"{self.server_url}/admin/player/{player_id}", 
                                  headers=headers, timeout=5)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"获取玩家详细信息失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"获取玩家详细信息异常: {e}")
            return None
    
    def refresh_monster_list(self):
        """刷新怪物列表"""
        try:
            with open(self.get_resource_path("data/game/monsters.json"), "r", encoding="utf-8") as f:
                monsters = json.load(f)

            # 清空现有列表
            for item in self.monster_tree.get_children():
                self.monster_tree.delete(item)

            # 添加怪物数据
            for monster_name, monster_data in monsters.items():
                if isinstance(monster_data, dict):
                    self.monster_tree.insert("", "end", values=(
                        monster_name,
                        monster_data.get("等级", ""),
                        monster_data.get("血量", ""),
                        monster_data.get("攻击下限", ""),
                        monster_data.get("攻击上限", ""),
                        monster_data.get("防御", ""),
                        monster_data.get("魔御", ""),
                        monster_data.get("经验", "")
                    ))

        except Exception as e:
            print(f"加载怪物数据失败: {e}")
            messagebox.showerror("错误", f"加载怪物数据失败: {e}")
    
    def on_monster_select(self, event):
        """怪物选择事件"""
        selection = self.monster_tree.selection()
        if not selection:
            return

        item = self.monster_tree.item(selection[0])
        monster_name = item['values'][0]

        try:
            with open(self.get_resource_path("data/game/monsters.json"), "r", encoding="utf-8") as f:
                monsters = json.load(f)

            if monster_name in monsters:
                monster_data = monsters[monster_name]
                # 填充所有输入框
                self.monster_name_var.set(monster_name)
                self.monster_level_var.set(str(monster_data.get("等级", "")))
                self.monster_hp_var.set(str(monster_data.get("血量", "")))
                self.monster_attack_min_var.set(str(monster_data.get("攻击下限", "")))
                self.monster_attack_max_var.set(str(monster_data.get("攻击上限", "")))
                self.monster_defense_var.set(str(monster_data.get("防御", "")))
                self.monster_magic_defense_var.set(str(monster_data.get("魔御", "")))
                self.monster_experience_var.set(str(monster_data.get("经验", "")))
                self.monster_agility_var.set(str(monster_data.get("敏捷", "")))
                self.monster_accuracy_var.set(str(monster_data.get("准确", "")))
                self.monster_move_speed_var.set(str(monster_data.get("移动速度", "")))
                self.monster_attack_speed_var.set(str(monster_data.get("攻击速度", "")))
                self.monster_undead_var.set(str(monster_data.get("不死系", "")))

        except Exception as e:
            print(f"加载怪物数据失败: {e}")
            messagebox.showerror("错误", f"加载怪物数据失败: {e}")
    
    def save_monster_data(self):
        """保存怪物数据"""
        try:
            monster_name = self.monster_name_var.get()
            if not monster_name:
                messagebox.showwarning("警告", "请输入怪物名称")
                return

            # 验证数值输入
            try:
                level = int(self.monster_level_var.get()) if self.monster_level_var.get() else 1
                hp = int(self.monster_hp_var.get()) if self.monster_hp_var.get() else 100
                attack_min = int(self.monster_attack_min_var.get()) if self.monster_attack_min_var.get() else 1
                attack_max = int(self.monster_attack_max_var.get()) if self.monster_attack_max_var.get() else 1
                defense = int(self.monster_defense_var.get()) if self.monster_defense_var.get() else 0
                magic_defense = int(self.monster_magic_defense_var.get()) if self.monster_magic_defense_var.get() else 0
                experience = int(self.monster_experience_var.get()) if self.monster_experience_var.get() else 10
                agility = int(self.monster_agility_var.get()) if self.monster_agility_var.get() else 10
                accuracy = int(self.monster_accuracy_var.get()) if self.monster_accuracy_var.get() else 5
                move_speed = int(self.monster_move_speed_var.get()) if self.monster_move_speed_var.get() else 1500
                attack_speed = int(self.monster_attack_speed_var.get()) if self.monster_attack_speed_var.get() else 3000
                undead = int(self.monster_undead_var.get()) if self.monster_undead_var.get() else 0
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数值")
                return

            # 验证攻击范围
            if attack_min > attack_max:
                messagebox.showwarning("警告", "攻击下限不能大于攻击上限")
                return

            with open(self.get_resource_path("data/game/monsters.json"), "r", encoding="utf-8") as f:
                monsters = json.load(f)

            if monster_name in monsters:
                # 更新怪物数据
                monsters[monster_name] = {
                    "等级": level,
                    "血量": hp,
                    "攻击下限": attack_min,
                    "攻击上限": attack_max,
                    "防御": defense,
                    "魔御": magic_defense,
                    "经验": experience,
                    "敏捷": agility,
                    "准确": accuracy,
                    "移动速度": move_speed,
                    "攻击速度": attack_speed,
                    "不死系": undead
                }

                with open(self.get_resource_path("data/game/monsters.json"), "w", encoding="utf-8") as f:
                    json.dump(monsters, f, ensure_ascii=False, indent=2)

                self.refresh_monster_list()
                messagebox.showinfo("成功", "怪物数据已保存")
            else:
                messagebox.showwarning("警告", "怪物不存在，请先添加怪物")

        except Exception as e:
            messagebox.showerror("错误", f"保存怪物数据失败: {e}")
    
    def add_monster(self):
        """添加怪物"""
        monster_name = self.monster_name_var.get()
        if not monster_name:
            messagebox.showwarning("警告", "请输入怪物名称")
            return

        try:
            # 验证数值输入
            try:
                level = int(self.monster_level_var.get()) if self.monster_level_var.get() else 1
                hp = int(self.monster_hp_var.get()) if self.monster_hp_var.get() else 100
                attack_min = int(self.monster_attack_min_var.get()) if self.monster_attack_min_var.get() else 1
                attack_max = int(self.monster_attack_max_var.get()) if self.monster_attack_max_var.get() else 1
                defense = int(self.monster_defense_var.get()) if self.monster_defense_var.get() else 0
                magic_defense = int(self.monster_magic_defense_var.get()) if self.monster_magic_defense_var.get() else 0
                experience = int(self.monster_experience_var.get()) if self.monster_experience_var.get() else 10
                agility = int(self.monster_agility_var.get()) if self.monster_agility_var.get() else 10
                accuracy = int(self.monster_accuracy_var.get()) if self.monster_accuracy_var.get() else 5
                move_speed = int(self.monster_move_speed_var.get()) if self.monster_move_speed_var.get() else 1500
                attack_speed = int(self.monster_attack_speed_var.get()) if self.monster_attack_speed_var.get() else 3000
                undead = int(self.monster_undead_var.get()) if self.monster_undead_var.get() else 0
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数值")
                return

            # 验证攻击范围
            if attack_min > attack_max:
                messagebox.showwarning("警告", "攻击下限不能大于攻击上限")
                return

            # 验证范围值
            if level < 1 or level > 1000:
                messagebox.showwarning("警告", "等级范围应在1-1000之间")
                return
            if hp < 1:
                messagebox.showwarning("警告", "血量必须大于0")
                return
            if undead not in [0, 1]:
                messagebox.showwarning("警告", "不死系只能为0或1")
                return

            with open(self.get_resource_path("data/game/monsters.json"), "r", encoding="utf-8") as f:
                monsters = json.load(f)

            if monster_name in monsters:
                messagebox.showwarning("警告", "怪物已存在")
                return

            # 添加新怪物（使用正确的中文字段名）
            monsters[monster_name] = {
                "等级": level,
                "血量": hp,
                "攻击下限": attack_min,
                "攻击上限": attack_max,
                "防御": defense,
                "魔御": magic_defense,
                "经验": experience,
                "敏捷": agility,
                "准确": accuracy,
                "移动速度": move_speed,
                "攻击速度": attack_speed,
                "不死系": undead
            }

            with open(self.get_resource_path("data/game/monsters.json"), "w", encoding="utf-8") as f:
                json.dump(monsters, f, ensure_ascii=False, indent=2)

            # 清空输入框
            self.clear_monster_inputs()
            self.refresh_monster_list()
            messagebox.showinfo("成功", f"怪物 '{monster_name}' 已添加")

        except Exception as e:
            messagebox.showerror("错误", f"添加怪物失败: {e}")
    
    def clear_monster_inputs(self):
        """清空怪物输入框"""
        self.monster_name_var.set("")
        self.monster_level_var.set("")
        self.monster_hp_var.set("")
        self.monster_attack_min_var.set("")
        self.monster_attack_max_var.set("")
        self.monster_defense_var.set("")
        self.monster_magic_defense_var.set("")
        self.monster_experience_var.set("")
        self.monster_agility_var.set("")
        self.monster_accuracy_var.set("")
        self.monster_move_speed_var.set("")
        self.monster_attack_speed_var.set("")
        self.monster_undead_var.set("")
    
    def delete_monster(self):
        """删除怪物"""
        selection = self.monster_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的怪物")
            return

        item = self.monster_tree.item(selection[0])
        monster_name = item['values'][0]

        if messagebox.askyesno("确认", f"确定要删除怪物 '{monster_name}' 吗？"):
            try:
                with open(self.get_resource_path("data/game/monsters.json"), "r", encoding="utf-8") as f:
                    monsters = json.load(f)

                if monster_name in monsters:
                    del monsters[monster_name]

                    with open(self.get_resource_path("data/game/monsters.json"), "w", encoding="utf-8") as f:
                        json.dump(monsters, f, ensure_ascii=False, indent=2)

                    self.refresh_monster_list()
                    messagebox.showinfo("成功", "怪物已删除")
                else:
                    messagebox.showwarning("警告", "怪物不存在")

            except Exception as e:
                messagebox.showerror("错误", f"删除怪物失败: {e}")
    
    def send_admin_command(self, command, data):
        """发送管理员命令"""
        if not self.admin_token:
            messagebox.showwarning("警告", "请先连接到服务器")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            response = requests.post(f"{self.server_url}/admin/command",
                                   json={"command": command, "data": data},
                                   headers=headers, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    messagebox.showinfo("成功", result.get("message", "命令执行成功"))
                    self.refresh_all()
                    return True
                else:
                    messagebox.showerror("错误", result.get("message", "命令执行失败"))
                    return False
            else:
                messagebox.showerror("错误", f"服务器错误: {response.status_code}")
                return False

        except Exception as e:
            messagebox.showerror("错误", f"发送命令失败: {e}")
            return False


def main():
    """主函数"""
    root = tk.Tk()
    app = GameAdminGUI(root)

    # 设置窗口图标
    try:
        root.iconbitmap("game_icon.ico")
    except:
        pass

    # 启动应用
    root.mainloop()


if __name__ == "__main__":
    main()
