# 传奇游戏云端系统部署指南

## 📋 系统概述

本云端系统将原本的本地聊天和邮件功能迁移到云端服务器，玩家只有在连接到服务器时才能使用这两个功能。

### 🏗️ 系统架构

```
客户端 (main.py)
    ↓
云连接管理器 (cloud_connection_manager.py)
    ↓
云端服务器 (cloud_server.py)
    ↓
PostgreSQL 数据库 + Redis 缓存
```

## 🚀 服务器端部署

### 1. 环境准备

```bash
# 安装 Python 3.8+
# 安装 PostgreSQL 12+
# 安装 Redis 6+

# 创建虚拟环境
python -m venv cloud_env
source cloud_env/bin/activate  # Linux/Mac
# cloud_env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements_cloud.txt
```

### 2. 数据库配置

#### PostgreSQL 配置

```sql
-- 创建数据库和用户
CREATE DATABASE game_cloud;
CREATE USER game_user WITH PASSWORD 'game_password';
GRANT ALL PRIVILEGES ON DATABASE game_cloud TO game_user;

-- 连接到数据库
\c game_cloud game_user

-- 表会在服务器首次启动时自动创建
```

#### Redis 配置

```bash
# 启动 Redis 服务
redis-server

# 或者使用配置文件
redis-server /path/to/redis.conf
```

### 3. 服务器配置

修改 `cloud_server.py` 中的数据库配置：

```python
def _get_database_configs(self) -> List[DatabaseConfig]:
    return [
        DatabaseConfig(
            host="你的数据库服务器IP",
            port=5432,
            database="game_cloud",
            username="game_user",
            password="你的数据库密码",
            role=DatabaseRole.MASTER,
            max_connections=20
        )
    ]
```

### 4. 启动服务器

```bash
# 开发环境
python cloud_server.py

# 或使用 uvicorn
uvicorn cloud_server:app --host 0.0.0.0 --port 8000

# 生产环境
gunicorn cloud_server:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 🖥️ 客户端配置

### 1. 文件结构

确保以下文件在客户端目录中：

```
游戏客户端/
├── main.py                         # 主程序（已修改）
├── cloud_connection_manager.py     # 云连接管理器
├── cloud_mail_gui.py              # 云端邮件GUI
├── cloud_chat_gui.py              # 云端聊天GUI
├── server_connection_gui.py        # 服务器连接GUI
├── cloud_server.py                # 云端服务器（可选，用于本地测试）
└── data/config/server_config.json  # 服务器配置
```

### 2. 服务器配置

修改 `data/config/server_config.json`：

```json
{
  "default_server": "http://你的服务器IP:8000",
  "backup_servers": [
    "http://你的服务器IP:8000",
    "http://备用服务器IP:8000"
  ],
  "auto_connect": false,
  "last_username": "",
  "version": "1.0.0"
}
```

### 3. 客户端依赖

```bash
pip install requests websockets
```

## 🎮 功能说明

### 主要变更

1. **本地聊天系统被移除** - 替换为云端实时聊天
2. **本地邮件系统被移除** - 替换为云端邮件系统
3. **新增服务器连接管理** - 玩家可以连接/断开服务器

### 功能特性

#### 🌐 服务器连接
- 支持多服务器配置
- 自动重连机制
- 连接状态实时监控
- 用户认证和注册

#### 💬 云端聊天系统
- 实时消息传输（WebSocket）
- 聊天历史记录
- 多用户同时在线
- 消息时间戳和发送者显示

#### 📧 云端邮件系统
- 邮件收发功能
- 附件奖励系统
- 邮件状态管理（未读/已读/已领取）
- 邮件过期机制

## 🔧 使用说明

### 玩家操作流程

1. **启动游戏**
   - 游戏启动后默认为本地模式
   - 聊天和邮件功能不可用

2. **连接服务器**
   - 点击 "🌐 连接服务器" 按钮
   - 输入服务器地址、用户名和密码
   - 首次使用需要注册账号

3. **使用云端功能**
   - 连接成功后，聊天和邮件按钮变为可用
   - 点击 "💬 聊天" 打开云端聊天室
   - 点击 "📬 邮件" 打开云端邮件系统

4. **断开连接**
   - 在服务器连接管理界面点击 "断开连接"
   - 或直接关闭游戏（自动断开）

### 注意事项

- 本地模式下聊天和邮件功能完全不可用
- 连接断开时会自动尝试重连
- 聊天消息和邮件数据完全存储在云端
- 游戏进度仍然保存在本地

## 🛠️ 开发和调试

### 本地测试

1. 启动本地服务器：
```bash
python cloud_server.py
```

2. 修改客户端配置连接到 `http://localhost:8000`

3. 注册测试账号并测试功能

### API 接口

服务器提供以下主要接口：

- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `GET /mail/list` - 获取邮件列表
- `GET /mail/{mail_id}` - 获取邮件详情
- `POST /mail/send` - 发送邮件
- `POST /mail/{mail_id}/action` - 邮件操作
- `WS /ws/chat` - WebSocket 聊天连接
- `GET /chat/history` - 获取聊天历史

### 数据库表结构

- `users` - 用户信息
- `cloud_mails` - 云端邮件
- `chat_messages` - 聊天消息
- `user_sessions` - 用户会话

## 🔒 安全考虑

1. **数据传输加密** - 生产环境建议使用 HTTPS/WSS
2. **密码安全** - 建议实现更强的密码哈希算法
3. **访问控制** - 可添加 IP 白名单和访问限制
4. **数据备份** - 定期备份数据库数据

## 📈 性能优化

1. **数据库优化** - 使用连接池和读写分离
2. **缓存机制** - 使用 Redis 缓存热点数据
3. **负载均衡** - 多服务器实例部署
4. **监控告警** - 添加性能监控和日志记录

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器地址和端口
   - 确认防火墙设置
   - 检查网络连通性

2. **认证失败**
   - 确认用户名密码正确
   - 检查用户是否已注册
   - 查看服务器日志

3. **功能异常**
   - 检查依赖包是否完整安装
   - 查看客户端和服务器错误日志
   - 确认数据库连接正常

### 日志位置

- 服务器日志：控制台输出
- 客户端日志：游戏内日志窗口
- 数据库日志：PostgreSQL 日志文件

## 📞 技术支持

如遇到问题，请提供以下信息：
- 详细的错误描述
- 操作步骤
- 错误日志
- 系统环境信息

---

## 总结

通过这个云端系统改造，实现了：

✅ **完全移除本地聊天和邮件功能**  
✅ **玩家必须连接服务器才能使用这两个功能**  
✅ **实时聊天和邮件同步**  
✅ **用户认证和数据安全**  
✅ **良好的用户体验和界面设计**  

系统现在完全符合您的需求：本地模式下聊天和邮件功能被禁用，只有连接到云端服务器时才能使用这些功能。 