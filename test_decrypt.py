#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_decrypt():
    """测试解密功能"""
    print("🧪 开始测试解密功能...")
    
    # 导入加密相关模块
    try:
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        from cryptography.fernet import Fernet
        import base64
        print("✅ 加密库导入成功")
    except ImportError as e:
        print(f"❌ 加密库导入失败: {e}")
        return
    
    # 测试解密存档文件
    save_path = Path("local_saves/local_player_auto_save.dat")
    if not save_path.exists():
        print(f"❌ 存档文件不存在: {save_path}")
        return
    
    print(f"📂 读取存档文件: {save_path}")
    with open(save_path, 'rb') as f:
        encrypted_data = f.read()
    
    print(f"📊 文件大小: {len(encrypted_data)} 字节")
    
    # 使用游戏中的解密逻辑
    password = "LegendOfMir2024GameSave"
    
    try:
        # 生成密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'salt_',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        
        # 解密
        fernet = Fernet(key)
        decrypted_data = fernet.decrypt(encrypted_data)
        decrypted_text = decrypted_data.decode('utf-8')
        
        print("✅ 解密成功!")
        print(f"📄 解密后数据长度: {len(decrypted_text)} 字符")
        print(f"📄 数据开头: {decrypted_text[:100]}...")
        
        # 尝试解析JSON
        import json
        save_data = json.loads(decrypted_text)
        print("✅ JSON解析成功!")
        print(f"📊 存档数据键: {list(save_data.keys())}")
        
        if "player" in save_data:
            player_data = save_data["player"]
            print(f"👤 玩家姓名: {player_data.get('name', '未知')}")
            print(f"🎯 玩家等级: {player_data.get('level', '未知')}")
            print(f"💼 玩家职业: {player_data.get('job_name', '未知')}")
        
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_decrypt()
