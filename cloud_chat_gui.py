#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端聊天系统GUI界面
只在连接到云端服务器时才可用，替代本地聊天系统
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import time
from datetime import datetime
from typing import Dict, List
from cloud_connection_manager import get_cloud_manager

class CloudChatGUI:
    """云端聊天系统GUI"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.cloud_manager = get_cloud_manager()
        self.window = None
        self.is_destroyed = False
        self.chat_text = None
        self.chat_entry = None
        
        # 检查云端连接状态
        if not self.cloud_manager.is_server_connected():
            messagebox.showwarning("未连接服务器", 
                                 "聊天系统需要连接到云端服务器才能使用\n请先点击'连接服务器'")
            return
        
        self.setup_gui()
        self.setup_callbacks()
        self.load_chat_history()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("云端聊天室")
        self.window.geometry("600x500")
        self.window.resizable(True, True)
        
        # 设置窗口图标（如果存在）
        try:
            self.window.iconbitmap("game_icon.ico")
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 顶部状态栏
        self.create_status_bar(main_frame)
        
        # 聊天显示区域
        self.create_chat_display(main_frame)
        
        # 消息输入区域
        self.create_input_area(main_frame)
        
        # 底部操作按钮
        self.create_action_buttons(main_frame)
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 定期检查连接状态
        self.check_connection_status()
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill="x", pady=(0, 10))
        
        # 连接状态指示器
        self.connection_label = ttk.Label(status_frame, text="")
        self.connection_label.pack(side="left")
        
        # 在线用户数（如果有的话）
        self.online_count_label = ttk.Label(status_frame, text="")
        self.online_count_label.pack(side="right")
        
        self.update_status_bar()
    
    def create_chat_display(self, parent):
        """创建聊天显示区域"""
        chat_frame = ttk.LabelFrame(parent, text="聊天记录", padding=10)
        chat_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 创建聊天文本框
        self.chat_text = scrolledtext.ScrolledText(
            chat_frame,
            wrap=tk.WORD,
            state="disabled",
            font=("Arial", 10),
            background="white",
            foreground="black"
        )
        self.chat_text.pack(fill="both", expand=True)
        
        # 配置不同类型消息的颜色标签
        self.chat_text.tag_configure("system", foreground="blue", font=("Arial", 10, "bold"))
        self.chat_text.tag_configure("user", foreground="green", font=("Arial", 10, "bold"))
        self.chat_text.tag_configure("others", foreground="purple", font=("Arial", 10, "bold"))
        self.chat_text.tag_configure("error", foreground="red", font=("Arial", 10, "bold"))
        self.chat_text.tag_configure("timestamp", foreground="gray", font=("Arial", 8))
    
    def create_input_area(self, parent):
        """创建消息输入区域"""
        input_frame = ttk.Frame(parent)
        input_frame.pack(fill="x", pady=(0, 10))
        
        # 消息输入框
        self.chat_entry = ttk.Entry(input_frame, font=("Arial", 10))
        self.chat_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        # 发送按钮
        self.send_button = ttk.Button(input_frame, text="发送", 
                                    command=self.send_message, width=8)
        self.send_button.pack(side="right")
        
        # 绑定回车键发送消息
        self.chat_entry.bind("<Return>", lambda e: self.send_message())
        
        # 设置输入框焦点
        self.chat_entry.focus_set()
    
    def create_action_buttons(self, parent):
        """创建操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill="x")
        
        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side="left")
        
        self.clear_button = ttk.Button(left_buttons, text="清空聊天", 
                                     command=self.clear_chat)
        self.clear_button.pack(side="left", padx=(0, 10))
        
        self.refresh_button = ttk.Button(left_buttons, text="刷新历史", 
                                       command=self.load_chat_history)
        self.refresh_button.pack(side="left", padx=(0, 10))
        
        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side="right")
        
        self.close_button = ttk.Button(right_buttons, text="关闭", command=self.on_close)
        self.close_button.pack(side="left")
    
    def setup_callbacks(self):
        """设置回调函数"""
        # 设置云端管理器的回调
        self.cloud_manager.set_callbacks(
            on_message_received=self.on_message_received,
            on_connection_status_changed=self.on_connection_status_changed
        )
    
    def update_status_bar(self):
        """更新状态栏"""
        if self.cloud_manager.is_server_connected():
            user_info = self.cloud_manager.user_info
            username = user_info.get('username', '未知用户') if user_info else '未知用户'
            self.connection_label.config(text=f"✅ 已连接云端服务器 - {username}", foreground="green")
        else:
            self.connection_label.config(text="❌ 未连接服务器", foreground="red")
        
        # 这里可以显示在线用户数，如果服务器支持的话
        self.online_count_label.config(text="全球聊天室")
    
    def check_connection_status(self):
        """定期检查连接状态"""
        if self.is_destroyed:
            return
        
        self.update_status_bar()
        
        # 如果断开连接，禁用相关功能
        if not self.cloud_manager.is_server_connected():
            self.disable_chat_functions()
            self.add_system_message("与服务器的连接已断开，聊天功能暂时不可用", "error")
        else:
            self.enable_chat_functions()
        
        # 每5秒检查一次
        if self.window and not self.is_destroyed:
            self.window.after(5000, self.check_connection_status)
    
    def disable_chat_functions(self):
        """禁用聊天功能"""
        if hasattr(self, 'chat_entry') and self.chat_entry:
            self.chat_entry.config(state="disabled")
        if hasattr(self, 'send_button') and self.send_button:
            self.send_button.config(state="disabled")
        if hasattr(self, 'refresh_button') and self.refresh_button:
            self.refresh_button.config(state="disabled")
    
    def enable_chat_functions(self):
        """启用聊天功能"""
        if hasattr(self, 'chat_entry') and self.chat_entry:
            self.chat_entry.config(state="normal")
        if hasattr(self, 'send_button') and self.send_button:
            self.send_button.config(state="normal")
        if hasattr(self, 'refresh_button') and self.refresh_button:
            self.refresh_button.config(state="normal")
    
    def load_chat_history(self):
        """加载聊天历史"""
        if not self.cloud_manager.is_server_connected():
            return
        
        try:
            # 清空当前聊天显示
            if self.chat_text:
                self.chat_text.config(state="normal")
                self.chat_text.delete(1.0, tk.END)
                self.chat_text.config(state="disabled")
            
            # 获取聊天历史
            chat_history = self.cloud_manager.get_chat_history()
            
            # 显示历史消息
            for message in chat_history:
                self.display_message(message)
            
            # 添加系统提示
            self.add_system_message("聊天历史加载完成", "system")
            
        except Exception as e:
            self.add_system_message(f"加载聊天历史失败: {str(e)}", "error")
    
    def send_message(self):
        """发送聊天消息"""
        if not self.chat_entry or not self.cloud_manager.is_server_connected():
            return
        
        message = self.chat_entry.get().strip()
        if not message:
            return
        
        try:
            # 通过云端管理器发送消息
            success = self.cloud_manager.send_chat_message(message)
            
            if success:
                # 清空输入框
                self.chat_entry.delete(0, tk.END)
                
                # 显示发送的消息（实际消息会通过WebSocket回显）
                user_info = self.cloud_manager.user_info
                username = user_info.get('username', '我') if user_info else '我'
                
                # 不在这里显示消息，等待服务器回显
                
            else:
                self.add_system_message("消息发送失败", "error")
                
        except Exception as e:
            self.add_system_message(f"发送消息失败: {str(e)}", "error")
    
    def on_message_received(self, message_data: dict):
        """接收到新消息的回调"""
        if self.is_destroyed or not self.window:
            return
        
        # 在GUI线程中显示消息
        self.window.after(0, lambda: self.display_message(message_data))
    
    def on_connection_status_changed(self, connected: bool, message: str):
        """连接状态变化回调"""
        if self.is_destroyed or not self.window:
            return
        
        # 在GUI线程中更新状态
        self.window.after(0, self.update_status_bar)
        
        if connected:
            self.window.after(0, lambda: self.add_system_message("重新连接到服务器", "system"))
        else:
            self.window.after(0, lambda: self.add_system_message("与服务器连接断开", "error"))
    
    def display_message(self, message_data: dict):
        """显示消息"""
        if not self.chat_text or self.is_destroyed:
            return
        
        try:
            sender_name = message_data.get('sender_name', '未知用户')
            message = message_data.get('message', '')
            timestamp = message_data.get('timestamp', '')
            
            # 格式化时间戳
            if timestamp:
                try:
                    if 'T' in timestamp:  # ISO格式
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        dt = datetime.fromtimestamp(float(timestamp))
                    time_str = dt.strftime("%H:%M:%S")
                except:
                    time_str = timestamp[:8] if len(timestamp) > 8 else timestamp
            else:
                time_str = time.strftime("%H:%M:%S")
            
            # 确定消息类型
            user_info = self.cloud_manager.user_info
            current_username = user_info.get('username', '') if user_info else ''
            
            if sender_name == current_username:
                sender_tag = "user"
            elif sender_name == "system":
                sender_tag = "system"
            else:
                sender_tag = "others"
            
            # 添加消息到聊天框
            self.chat_text.config(state="normal")
            
            # 插入时间戳
            self.chat_text.insert(tk.END, f"[{time_str}] ", "timestamp")
            
            # 插入发送者名称
            self.chat_text.insert(tk.END, f"{sender_name}: ", sender_tag)
            
            # 插入消息内容
            self.chat_text.insert(tk.END, f"{message}\n")
            
            self.chat_text.config(state="disabled")
            
            # 滚动到底部
            self.chat_text.see(tk.END)
            
        except Exception as e:
            print(f"显示消息失败: {e}")
    
    def add_system_message(self, message: str, tag: str = "system"):
        """添加系统消息"""
        if not self.chat_text or self.is_destroyed:
            return
        
        try:
            time_str = time.strftime("%H:%M:%S")
            
            self.chat_text.config(state="normal")
            self.chat_text.insert(tk.END, f"[{time_str}] ", "timestamp")
            self.chat_text.insert(tk.END, f"系统: {message}\n", tag)
            self.chat_text.config(state="disabled")
            self.chat_text.see(tk.END)
            
        except Exception as e:
            print(f"添加系统消息失败: {e}")
    
    def clear_chat(self):
        """清空聊天记录"""
        if not self.chat_text:
            return
        
        if messagebox.askyesno("确认清空", "确定要清空本地聊天记录吗？\n(这不会影响服务器上的聊天历史)"):
            self.chat_text.config(state="normal")
            self.chat_text.delete(1.0, tk.END)
            self.chat_text.config(state="disabled")
            
            self.add_system_message("本地聊天记录已清空", "system")
    
    def on_close(self):
        """关闭窗口"""
        if self.window:
            self.is_destroyed = True
            
            # 移除回调
            self.cloud_manager.set_callbacks(
                on_message_received=None,
                on_connection_status_changed=None
            )
            
            self.window.destroy()
            self.window = None

def show_cloud_chat_gui(parent=None):
    """显示云端聊天GUI"""
    return CloudChatGUI(parent) 