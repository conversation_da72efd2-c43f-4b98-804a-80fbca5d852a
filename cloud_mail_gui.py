#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端邮件系统GUI界面
只在连接到云端服务器时才可用，替代本地邮件系统
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
from datetime import datetime
from typing import Dict, List, Optional
from cloud_connection_manager import get_cloud_manager

class CloudMailGUI:
    """云端邮件系统GUI"""
    
    def __init__(self, player_id: str, parent=None):
        self.player_id = player_id
        self.parent = parent
        self.cloud_manager = get_cloud_manager()
        self.current_mails = []
        self.selected_mail = None
        self.window = None
        self.is_destroyed = False
        
        # 检查云端连接状态
        if not self.cloud_manager.is_server_connected():
            messagebox.showwarning("未连接服务器", 
                                 "邮件系统需要连接到云端服务器才能使用\n请先点击'连接服务器'")
            return
        
        self.setup_gui()
        self.refresh_mail_list()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("云端邮件系统")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 设置窗口图标（如果存在）
        try:
            self.window.iconbitmap("game_icon.ico")
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 顶部状态栏
        self.create_status_bar(main_frame)
        
        # 创建邮件列表框架
        self.create_mail_list_frame(main_frame)
        
        # 创建邮件详情框架
        self.create_mail_detail_frame(main_frame)
        
        # 底部操作按钮
        self.create_action_buttons(main_frame)
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 定期检查连接状态
        self.check_connection_status()
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill="x", pady=(0, 10))
        
        # 连接状态指示器
        self.connection_label = ttk.Label(status_frame, text="")
        self.connection_label.pack(side="left")
        
        # 邮件统计
        self.mail_count_label = ttk.Label(status_frame, text="")
        self.mail_count_label.pack(side="right")
        
        self.update_status_bar()
    
    def create_mail_list_frame(self, parent):
        """创建邮件列表框架"""
        list_frame = ttk.LabelFrame(parent, text="邮件列表", padding=10)
        list_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 创建Treeview用于显示邮件列表
        columns = ("title", "sender", "status", "rewards", "date")
        self.mail_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        
        # 定义列标题
        self.mail_tree.heading("title", text="标题")
        self.mail_tree.heading("sender", text="发件人")
        self.mail_tree.heading("status", text="状态")
        self.mail_tree.heading("rewards", text="奖励")
        self.mail_tree.heading("date", text="日期")
        
        # 设置列宽
        self.mail_tree.column("title", width=250)
        self.mail_tree.column("sender", width=100)
        self.mail_tree.column("status", width=80)
        self.mail_tree.column("rewards", width=80)
        self.mail_tree.column("date", width=120)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.mail_tree.yview)
        self.mail_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.mail_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.mail_tree.bind("<<TreeviewSelect>>", self.on_mail_select)
        self.mail_tree.bind("<Double-1>", self.on_mail_double_click)
    
    def create_mail_detail_frame(self, parent):
        """创建邮件详情框架"""
        detail_frame = ttk.LabelFrame(parent, text="邮件详情", padding=10)
        detail_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 邮件标题
        title_frame = ttk.Frame(detail_frame)
        title_frame.pack(fill="x", pady=(0, 5))
        
        ttk.Label(title_frame, text="标题:", font=("Arial", 10, "bold")).pack(side="left")
        self.title_label = ttk.Label(title_frame, text="", foreground="blue")
        self.title_label.pack(side="left", padx=(5, 0))
        
        # 发件人和日期
        info_frame = ttk.Frame(detail_frame)
        info_frame.pack(fill="x", pady=(0, 5))
        
        ttk.Label(info_frame, text="发件人:").pack(side="left")
        self.sender_label = ttk.Label(info_frame, text="")
        self.sender_label.pack(side="left", padx=(5, 20))
        
        ttk.Label(info_frame, text="日期:").pack(side="left")
        self.date_label = ttk.Label(info_frame, text="")
        self.date_label.pack(side="left", padx=(5, 0))
        
        # 邮件内容
        content_frame = ttk.Frame(detail_frame)
        content_frame.pack(fill="both", expand=True, pady=(5, 0))
        
        ttk.Label(content_frame, text="内容:").pack(anchor="w")
        self.content_text = scrolledtext.ScrolledText(
            content_frame, 
            height=8, 
            wrap=tk.WORD,
            state="disabled",
            font=("Arial", 10)
        )
        self.content_text.pack(fill="both", expand=True, pady=(5, 0))
        
        # 奖励显示框架
        self.rewards_frame = ttk.LabelFrame(detail_frame, text="附件奖励")
        self.rewards_frame.pack(fill="x", pady=(10, 0))
        self.rewards_frame.pack_forget()  # 初始隐藏
    
    def create_action_buttons(self, parent):
        """创建操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill="x", pady=(10, 0))
        
        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side="left")
        
        self.refresh_button = ttk.Button(left_buttons, text="刷新", command=self.refresh_mail_list)
        self.refresh_button.pack(side="left", padx=(0, 10))
        
        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side="right")
        
        self.claim_button = ttk.Button(right_buttons, text="领取奖励", 
                                     command=self.claim_rewards, state="disabled")
        self.claim_button.pack(side="left", padx=(0, 10))
        
        self.delete_button = ttk.Button(right_buttons, text="删除", 
                                      command=self.delete_mail, state="disabled")
        self.delete_button.pack(side="left", padx=(0, 10))
        
        self.close_button = ttk.Button(right_buttons, text="关闭", command=self.on_close)
        self.close_button.pack(side="left")
    
    def update_status_bar(self):
        """更新状态栏"""
        if self.cloud_manager.is_server_connected():
            self.connection_label.config(text="✅ 已连接云端服务器", foreground="green")
        else:
            self.connection_label.config(text="❌ 未连接服务器", foreground="red")
        
        mail_count = len(self.current_mails)
        unread_count = len([m for m in self.current_mails if m.get('status') == 'unread'])
        self.mail_count_label.config(text=f"总计: {mail_count} | 未读: {unread_count}")
    
    def check_connection_status(self):
        """定期检查连接状态"""
        if self.is_destroyed:
            return
        
        self.update_status_bar()
        
        # 如果断开连接，禁用相关功能
        if not self.cloud_manager.is_server_connected():
            self.disable_mail_functions()
        else:
            self.enable_mail_functions()
        
        # 每5秒检查一次
        if self.window and not self.is_destroyed:
            self.window.after(5000, self.check_connection_status)
    
    def disable_mail_functions(self):
        """禁用邮件功能"""
        self.refresh_button.config(state="disabled")
        self.claim_button.config(state="disabled")
        self.delete_button.config(state="disabled")
    
    def enable_mail_functions(self):
        """启用邮件功能"""
        self.refresh_button.config(state="normal")
        # 其他按钮根据选中的邮件状态决定
    
    def refresh_mail_list(self):
        """刷新邮件列表"""
        if not self.cloud_manager.is_server_connected():
            messagebox.showwarning("连接错误", "未连接到云端服务器")
            return
        
        try:
            # 从云端获取邮件列表
            self.current_mails = self.cloud_manager.get_mail_list()
            
            # 清空现有列表
            for item in self.mail_tree.get_children():
                self.mail_tree.delete(item)
            
            # 添加邮件到列表
            for mail in self.current_mails:
                mail_id = mail.get('mail_id', '')
                title = mail.get('title', '无标题')
                sender = mail.get('sender_id', 'system')
                status = self.get_status_text(mail.get('status', 'unread'))
                has_rewards = "✓" if mail.get('has_rewards', False) else ""
                
                # 格式化日期
                created_at = mail.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        date_str = date_obj.strftime("%m-%d %H:%M")
                    except:
                        date_str = created_at[:10]  # 显示日期部分
                else:
                    date_str = ""
                
                # 根据状态设置标签
                tags = []
                if mail.get('status') == 'unread':
                    tags.append('unread')
                elif mail.get('has_rewards') and mail.get('status') != 'claimed':
                    tags.append('has_rewards')
                
                self.mail_tree.insert("", "end", iid=mail_id, 
                                    values=(title, sender, status, has_rewards, date_str),
                                    tags=tags)
            
            # 配置标签样式
            self.mail_tree.tag_configure('unread', background='#E6F3FF')
            self.mail_tree.tag_configure('has_rewards', background='#FFE6E6')
            
            self.update_status_bar()
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新邮件列表失败: {str(e)}")
    
    def get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            'unread': '未读',
            'read': '已读',
            'claimed': '已领取'
        }
        return status_map.get(status, status)
    
    def on_mail_select(self, event):
        """邮件选择事件"""
        selection = self.mail_tree.selection()
        if not selection:
            self.selected_mail = None
            self.clear_mail_detail()
            self.update_action_buttons()
            return
        
        mail_id = selection[0]
        self.selected_mail = next((m for m in self.current_mails if m.get('mail_id') == mail_id), None)
        
        if self.selected_mail:
            self.load_mail_detail(mail_id)
        
        self.update_action_buttons()
    
    def on_mail_double_click(self, event):
        """邮件双击事件"""
        if self.selected_mail and self.selected_mail.get('has_rewards') and \
           self.selected_mail.get('status') != 'claimed':
            self.claim_rewards()
    
    def load_mail_detail(self, mail_id: str):
        """加载邮件详情"""
        if not self.cloud_manager.is_server_connected():
            return
        
        try:
            mail_detail = self.cloud_manager.get_mail_detail(mail_id)
            if not mail_detail:
                messagebox.showerror("错误", "无法获取邮件详情")
                return
            
            # 更新详情显示
            self.title_label.config(text=mail_detail.get('title', ''))
            self.sender_label.config(text=mail_detail.get('sender_id', 'system'))
            
            # 格式化日期
            created_at = mail_detail.get('created_at', '')
            if created_at:
                try:
                    date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    date_str = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    date_str = created_at
            else:
                date_str = "未知"
            self.date_label.config(text=date_str)
            
            # 显示内容
            self.content_text.config(state="normal")
            self.content_text.delete(1.0, tk.END)
            self.content_text.insert(1.0, mail_detail.get('content', ''))
            self.content_text.config(state="disabled")
            
            # 显示奖励信息
            self.display_rewards(mail_detail.get('rewards'))
            
            # 更新本地邮件状态
            for mail in self.current_mails:
                if mail.get('mail_id') == mail_id:
                    mail['status'] = mail_detail.get('status', mail.get('status'))
                    break
            
        except Exception as e:
            messagebox.showerror("错误", f"加载邮件详情失败: {str(e)}")
    
    def display_rewards(self, rewards_data):
        """显示奖励信息"""
        # 清除现有奖励显示
        for widget in self.rewards_frame.winfo_children():
            widget.destroy()
        
        if not rewards_data:
            self.rewards_frame.pack_forget()
            return
        
        try:
            if isinstance(rewards_data, str):
                rewards = json.loads(rewards_data)
            else:
                rewards = rewards_data
            
            if not rewards:
                self.rewards_frame.pack_forget()
                return
            
            self.rewards_frame.pack(fill="x", pady=(10, 0))
            
            # 创建奖励列表
            rewards_text = tk.Text(self.rewards_frame, height=3, wrap=tk.WORD)
            rewards_text.pack(fill="x", padx=5, pady=5)
            
            reward_lines = []
            for item_type, items in rewards.items():
                if isinstance(items, dict):
                    for item_name, quantity in items.items():
                        reward_lines.append(f"• {item_name} x{quantity}")
                else:
                    reward_lines.append(f"• {item_type}: {items}")
            
            rewards_text.insert(1.0, "\n".join(reward_lines))
            rewards_text.config(state="disabled")
            
        except Exception as e:
            print(f"显示奖励信息失败: {e}")
            self.rewards_frame.pack_forget()
    
    def clear_mail_detail(self):
        """清除邮件详情显示"""
        self.title_label.config(text="")
        self.sender_label.config(text="")
        self.date_label.config(text="")
        self.content_text.config(state="normal")
        self.content_text.delete(1.0, tk.END)
        self.content_text.config(state="disabled")
        self.rewards_frame.pack_forget()
    
    def update_action_buttons(self):
        """更新操作按钮状态"""
        if not self.cloud_manager.is_server_connected():
            self.claim_button.config(state="disabled")
            self.delete_button.config(state="disabled")
            return
        
        if self.selected_mail:
            # 领取奖励按钮
            if (self.selected_mail.get('has_rewards', False) and 
                self.selected_mail.get('status') != 'claimed'):
                self.claim_button.config(state="normal")
            else:
                self.claim_button.config(state="disabled")
            
            # 删除按钮
            self.delete_button.config(state="normal")
        else:
            self.claim_button.config(state="disabled")
            self.delete_button.config(state="disabled")
    
    def claim_rewards(self):
        """领取奖励"""
        if not self.selected_mail or not self.cloud_manager.is_server_connected():
            return
        
        mail_id = self.selected_mail.get('mail_id')
        if not mail_id:
            return
        
        try:
            rewards = self.cloud_manager.claim_mail_reward(mail_id)
            if rewards:
                # 显示奖励获得提示
                reward_text = self.format_rewards_text(rewards)
                messagebox.showinfo("奖励领取成功", f"您获得了以下奖励:\n\n{reward_text}")
                
                # 刷新邮件列表
                self.refresh_mail_list()
                
                # 清除选择
                self.mail_tree.selection_remove(self.mail_tree.selection())
                self.clear_mail_detail()
            else:
                messagebox.showerror("错误", "领取奖励失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"领取奖励失败: {str(e)}")
    
    def format_rewards_text(self, rewards: dict) -> str:
        """格式化奖励文本"""
        lines = []
        for item_type, items in rewards.items():
            if isinstance(items, dict):
                for item_name, quantity in items.items():
                    lines.append(f"• {item_name} x{quantity}")
            else:
                lines.append(f"• {item_type}: {items}")
        return "\n".join(lines)
    
    def delete_mail(self):
        """删除邮件"""
        if not self.selected_mail or not self.cloud_manager.is_server_connected():
            return
        
        mail_id = self.selected_mail.get('mail_id')
        title = self.selected_mail.get('title', '未知邮件')
        
        if not messagebox.askyesno("确认删除", f"确定要删除邮件 '{title}' 吗？"):
            return
        
        try:
            if self.cloud_manager.delete_mail(mail_id):
                messagebox.showinfo("成功", "邮件删除成功")
                self.refresh_mail_list()
                self.clear_mail_detail()
            else:
                messagebox.showerror("错误", "删除邮件失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除邮件失败: {str(e)}")
    
    def on_close(self):
        """关闭窗口"""
        if self.window:
            self.is_destroyed = True
            self.window.destroy()
            self.window = None

def show_cloud_mail_gui(player_id: str, parent=None):
    """显示云端邮件GUI"""
    return CloudMailGUI(player_id, parent) 