#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装备强化系统
提供装备强化、成功率计算、属性加成等功能
"""

import json
import random
import math
import sys
import os
from typing import Optional, Tuple, Dict, Any
import logging

class EnhancementResult:
    """强化结果类"""
    def __init__(self, success: bool, new_level: int, destroyed: bool = False, 
                 message: str = "", cost: int = 0):
        self.success = success
        self.new_level = new_level
        self.destroyed = destroyed
        self.message = message
        self.cost = cost

class EnhancementSystem:
    """装备强化系统核心类"""
    
    def __init__(self, game_instance=None):
        self.game = game_instance
        self.logger = logging.getLogger(__name__)
        self.config = self.load_enhancement_config()
        
    def load_enhancement_config(self) -> Dict[str, Any]:
        """加载强化配置文件"""
        try:
            config_path = self.get_resource_path('data/game/enhancement_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)['enhancement_config']
        except FileNotFoundError:
            self.logger.error("找不到强化配置文件")
            return self._get_default_config()
        except json.JSONDecodeError:
            self.logger.error("强化配置文件格式错误")
            return self._get_default_config()
    
    def get_resource_path(self, relative_path):
        """获取资源文件的绝对路径，兼容PyInstaller打包"""
        try:
            # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
            base_path = sys._MEIPASS
        except Exception:
            # 如果不是打包环境，使用当前脚本目录
            base_path = os.path.abspath(".")
        
        return os.path.join(base_path, relative_path)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "success_rates": {str(i): max(100 - i * 10, 10) for i in range(16)},
            "enhancement_costs": {"武器": [i * 1000 for i in range(16)]},
            "attribute_bonus_per_level": {"attack": 0.08},
            "max_enhancement_level": 15
        }
    
    def can_enhance(self, equipment, player) -> Tuple[bool, str]:
        """检查是否可以强化装备"""
        if not hasattr(equipment, 'enhancement_level'):
            return False, "该装备不支持强化"
        
        if equipment.enhancement_level >= self.config.get("max_enhancement_level", 15):
            return False, "装备已达到最大强化等级"
        
        cost = self.calculate_enhancement_cost(equipment)
        if player.gold < cost:
            return False, f"金币不足，需要 {cost} 金币"
        
        return True, "可以强化"
    
    def calculate_enhancement_cost(self, equipment) -> int:
        """计算强化费用"""
        equipment_type = self._get_equipment_type(equipment)
        level = getattr(equipment, 'enhancement_level', 0)
        
        costs = self.config.get("enhancement_costs", {}).get(equipment_type, [])
        if level < len(costs):
            return costs[level]
        
        # 如果超出配置范围，使用公式计算
        base_cost = costs[-1] if costs else 1000
        return int(base_cost * (1.5 ** (level - len(costs) + 1)))
    
    def calculate_success_rate(self, equipment, has_luck_item: bool = False, 
                             luck_bonus: int = 0) -> float:
        """计算强化成功率"""
        level = getattr(equipment, 'enhancement_level', 0)
        base_rate = self.config.get("success_rates", {}).get(str(level), 50)
        
        # 应用幸运道具加成
        final_rate = base_rate + luck_bonus
        
        # 确保成功率在合理范围内
        return max(0, min(100, final_rate))
    
    def enhance_equipment(self, equipment, player, use_protection: bool = False, 
                         luck_item_name: str = None) -> EnhancementResult:
        """执行装备强化"""
        # 检查强化条件
        can_enhance, reason = self.can_enhance(equipment, player)
        if not can_enhance:
            return EnhancementResult(False, equipment.enhancement_level, False, reason)
        
        # 计算费用和成功率
        cost = self.calculate_enhancement_cost(equipment)
        luck_bonus = self._get_luck_bonus(luck_item_name) if luck_item_name else 0
        success_rate = self.calculate_success_rate(equipment, luck_item_name is not None, luck_bonus)
        
        # 扣除金币
        player.gold -= cost
        
        # 消耗幸运道具
        if luck_item_name:
            self._consume_luck_item(player, luck_item_name)
        
        # 执行强化
        current_level = equipment.enhancement_level
        is_success = random.randint(1, 100) <= success_rate
        
        if is_success:
            # 强化成功
            equipment.enhancement_level += 1
            self._apply_enhancement_bonus(equipment)
            message = f"强化成功！{equipment.name} 现在是 +{equipment.enhancement_level}"
            return EnhancementResult(True, equipment.enhancement_level, False, message, cost)
        else:
            # 强化失败
            return self._handle_enhancement_failure(equipment, current_level, use_protection, player, cost)
    
    def _handle_enhancement_failure(self, equipment, original_level: int, 
                                   use_protection: bool, player, cost: int) -> EnhancementResult:
        """处理强化失败"""
        penalty = self._get_failure_penalty(original_level)
        
        # 检查是否使用保护符
        if use_protection:
            protection_result = self._use_protection_item(player, penalty)
            if protection_result:
                message = f"强化失败，但保护符生效！{equipment.name} 等级未降低"
                return EnhancementResult(False, equipment.enhancement_level, False, message, cost)
        
        # 应用失败惩罚
        if penalty["destroy_chance"] > 0 and random.randint(1, 100) <= penalty["destroy_chance"]:
            # 装备损坏
            message = f"强化失败！{equipment.name} 已损坏"
            return EnhancementResult(False, 0, True, message, cost)
        
        # 等级下降
        drop_levels = penalty["drop_level"]
        if drop_levels > 0:
            equipment.enhancement_level = max(0, equipment.enhancement_level - drop_levels)
            self._apply_enhancement_bonus(equipment)
            message = f"强化失败！{equipment.name} 等级降至 +{equipment.enhancement_level}"
        else:
            message = f"强化失败！{equipment.name} 等级未变化"
        
        return EnhancementResult(False, equipment.enhancement_level, False, message, cost)
    
    def _get_failure_penalty(self, level: int) -> Dict[str, int]:
        """获取失败惩罚"""
        penalties = self.config.get("failure_penalties", {})
        
        if level <= 6:
            return penalties.get("0-6", {"drop_level": 0, "destroy_chance": 0})
        elif level <= 9:
            return penalties.get("7-9", {"drop_level": 1, "destroy_chance": 0})
        elif level <= 12:
            return penalties.get("10-12", {"drop_level": 1, "destroy_chance": 5})
        else:
            return penalties.get("13-15", {"drop_level": 2, "destroy_chance": 10})
    
    def _get_luck_bonus(self, luck_item_name: str) -> int:
        """获取幸运道具加成"""
        luck_items = self.config.get("luck_items", {})
        return luck_items.get(luck_item_name, {}).get("success_rate_bonus", 0)
    
    def _consume_luck_item(self, player, luck_item_name: str):
        """消耗幸运道具"""
        luck_config = self.config.get("luck_items", {}).get(luck_item_name, {})
        if luck_config.get("consumed_on_use", True):
            # 从背包中移除道具
            for item in player.inventory:
                if hasattr(item, 'name') and item.name == luck_item_name:
                    player.inventory.remove(item)
                    break
    
    def _use_protection_item(self, player, penalty: Dict[str, int]) -> bool:
        """使用保护道具"""
        # 寻找保护符
        for item in player.inventory:
            if hasattr(item, 'name') and item.name in ["保护符", "完美保护符"]:
                protection_config = self.config.get("protection_items", {}).get(item.name, {})
                
                # 检查保护效果
                if penalty["destroy_chance"] > 0 and not protection_config.get("prevent_destroy", False):
                    continue
                if penalty["drop_level"] > 0 and not protection_config.get("prevent_drop", False):
                    continue
                
                # 消耗保护符
                if protection_config.get("consumed_on_failure", True):
                    player.inventory.remove(item)
                
                return True
        
        return False
    
    def _apply_enhancement_bonus(self, equipment):
        """应用强化属性加成"""
        if not hasattr(equipment, 'enhancement_level') or equipment.enhancement_level <= 0:
            return
        
        # 获取基础属性（如果没有保存过，则使用当前属性作为基础）
        if not hasattr(equipment, 'base_stats') or equipment.base_stats is None:
            equipment.base_stats = self._save_base_stats(equipment)
        
        # 计算强化加成
        bonus_config = self.config.get("attribute_bonus_per_level", {})
        level = equipment.enhancement_level
        
        # 应用加成到各属性
        self._apply_stat_bonus(equipment, 'attack', bonus_config.get('attack', 0.08), level)
        self._apply_stat_bonus(equipment, 'magic', bonus_config.get('magic', 0.08), level)
        self._apply_stat_bonus(equipment, 'taoism', bonus_config.get('taoism', 0.08), level)
        self._apply_stat_bonus(equipment, 'defense', bonus_config.get('defense', 0.06), level)
        self._apply_stat_bonus(equipment, 'magic_defense', bonus_config.get('magic_defense', 0.06), level)
        self._apply_stat_bonus(equipment, 'accuracy', bonus_config.get('accuracy', 0.05), level)
        self._apply_stat_bonus(equipment, 'agility', bonus_config.get('agility', 0.05), level)
        self._apply_stat_bonus(equipment, 'luck', bonus_config.get('luck', 0.03), level)
        self._apply_stat_bonus(equipment, 'hp_restore', bonus_config.get('hp_restore', 0.10), level)
        self._apply_stat_bonus(equipment, 'mp_restore', bonus_config.get('mp_restore', 0.10), level)
    
    def _save_base_stats(self, equipment) -> Dict[str, Any]:
        """保存装备的基础属性"""
        base_stats = {}
        
        # 保存可能被强化的属性
        stat_attrs = ['attack', 'magic', 'taoism', 'defense', 'magic_defense', 
                     'accuracy', 'agility', 'luck', 'hp_restore', 'mp_restore']
        
        for attr in stat_attrs:
            if hasattr(equipment, attr):
                base_stats[attr] = getattr(equipment, attr)
        
        return base_stats
    
    def _apply_stat_bonus(self, equipment, stat_name: str, bonus_rate: float, level: int):
        """应用单个属性的强化加成"""
        if not hasattr(equipment, stat_name):
            return
        
        # 确保base_stats存在
        if not hasattr(equipment, 'base_stats') or equipment.base_stats is None:
            equipment.base_stats = self._save_base_stats(equipment)
        
        base_value = equipment.base_stats.get(stat_name)
        if base_value is None:
            return
        
        # 计算强化后的值
        if isinstance(base_value, list) and len(base_value) == 2:
            # 范围属性 [min, max]
            min_val, max_val = base_value
            bonus_multiplier = 1 + (bonus_rate * level)
            enhanced_min = int(min_val * bonus_multiplier)
            enhanced_max = int(max_val * bonus_multiplier)
            setattr(equipment, stat_name, [enhanced_min, enhanced_max])
            
            # 同时更新对应的原始属性名称
            if stat_name == 'attack' and hasattr(equipment, 'damage_range'):
                equipment.damage_range = [enhanced_min, enhanced_max]
            elif stat_name == 'magic' and hasattr(equipment, 'magic_damage_range'):
                equipment.magic_damage_range = [enhanced_min, enhanced_max]
            elif stat_name == 'taoism' and hasattr(equipment, 'taoist_damage_range'):
                equipment.taoist_damage_range = [enhanced_min, enhanced_max]
            elif stat_name == 'defense' and hasattr(equipment, 'defense_range'):
                equipment.defense_range = [enhanced_min, enhanced_max]
            elif stat_name == 'magic_defense' and hasattr(equipment, 'magic_defense_range'):
                equipment.magic_defense_range = [enhanced_min, enhanced_max]
                
        elif isinstance(base_value, (int, float)):
            # 单一数值属性
            bonus_multiplier = 1 + (bonus_rate * level)
            enhanced_value = int(base_value * bonus_multiplier) if isinstance(base_value, int) else base_value * bonus_multiplier
            setattr(equipment, stat_name, enhanced_value)
    
    def _get_equipment_type(self, equipment) -> str:
        """获取装备类型用于费用计算"""
        class_name = equipment.__class__.__name__
        
        type_mapping = {
            'Weapon': '武器',
            'Armor': '防具',
            'Helmet': '头盔',
            'Bracelet': '手镯',
            'Ring': '戒指',
            'Necklace': '项链',
            'Medal': '勋章',
            'MagicStone': '魔石'
        }
        
        return type_mapping.get(class_name, '武器')
    
    def get_enhancement_preview(self, equipment) -> Dict[str, Any]:
        """获取强化预览信息"""
        if not hasattr(equipment, 'enhancement_level'):
            return {}
        
        current_level = equipment.enhancement_level
        if current_level >= self.config.get("max_enhancement_level", 15):
            return {"message": "已达到最大强化等级"}
        
        # 计算下一级的属性
        preview_equipment = self._create_preview_equipment(equipment)
        preview_equipment.enhancement_level = current_level + 1
        self._apply_enhancement_bonus(preview_equipment)
        
        return {
            "current_level": current_level,
            "next_level": current_level + 1,
            "success_rate": self.calculate_success_rate(equipment),
            "cost": self.calculate_enhancement_cost(equipment),
            "current_stats": self._get_equipment_stats(equipment),
            "next_stats": self._get_equipment_stats(preview_equipment)
        }
    
    def _create_preview_equipment(self, equipment):
        """创建用于预览的装备副本"""
        import copy
        return copy.deepcopy(equipment)
    
    def _get_equipment_stats(self, equipment) -> Dict[str, Any]:
        """获取装备属性字典"""
        stats = {}
        stat_attrs = ['attack', 'magic', 'taoism', 'defense', 'magic_defense', 
                     'accuracy', 'agility', 'luck', 'hp_restore', 'mp_restore']
        
        for attr in stat_attrs:
            if hasattr(equipment, attr):
                value = getattr(equipment, attr)
                if value is not None and (isinstance(value, (int, float)) and value != 0) or \
                   (isinstance(value, list) and any(v != 0 for v in value)):
                    stats[attr] = value
        
        return stats