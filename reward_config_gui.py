#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奖励配置管理界面
提供图形化界面来管理奖励配置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from typing import Dict, Any, Optional
from reward_config_manager import RewardConfigManager
from mail_reward_service import MailRewardService

class RewardConfigGUI:
    """奖励配置管理界面"""
    
    def __init__(self, master=None):
        self.master = master or tk.Tk()
        self.master.title("奖励配置管理器")
        self.master.geometry("1000x700")
        
        # 初始化配置管理器
        self.config_manager = RewardConfigManager()
        self.reward_service = MailRewardService(self.config_manager)
        
        # 当前编辑的配置
        self.current_config_type = None
        self.current_config_data = None
        
        # 创建界面
        self.create_widgets()
        self.load_config_list()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧配置列表
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        ttk.Label(left_frame, text="配置文件列表", font=("Arial", 12, "bold")).pack(pady=(0, 10))
        
        # 配置列表
        self.config_listbox = tk.Listbox(left_frame, width=25, height=15)
        self.config_listbox.pack(fill=tk.Y, expand=True)
        self.config_listbox.bind('<<ListboxSelect>>', self.on_config_select)
        
        # 配置操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="刷新列表", command=self.load_config_list).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="新建配置", command=self.create_new_config).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="导入配置", command=self.import_config).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="导出配置", command=self.export_config).pack(fill=tk.X, pady=(0, 5))
        
        # 右侧编辑区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 配置编辑标题
        title_frame = ttk.Frame(right_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.config_title_label = ttk.Label(title_frame, text="请选择配置文件", font=("Arial", 14, "bold"))
        self.config_title_label.pack(side=tk.LEFT)
        
        # 操作按钮
        action_frame = ttk.Frame(title_frame)
        action_frame.pack(side=tk.RIGHT)
        
        ttk.Button(action_frame, text="保存", command=self.save_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="重置", command=self.reset_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="测试", command=self.test_config).pack(side=tk.LEFT)
        
        # 创建笔记本控件用于不同配置类型的编辑
        self.notebook = ttk.Notebook(right_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建不同配置类型的编辑页面
        self.create_level_up_editor()
        self.create_welcome_editor()
        self.create_special_editor()
        self.create_json_editor()
    
    def create_level_up_editor(self):
        """创建升级奖励编辑器"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="升级奖励")
        
        # 滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基础奖励设置
        base_frame = ttk.LabelFrame(scrollable_frame, text="基础奖励公式")
        base_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(base_frame, text="金币公式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.level_gold_formula = tk.StringVar(value="{level} * 1000")
        ttk.Entry(base_frame, textvariable=self.level_gold_formula, width=30).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(base_frame, text="经验公式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.level_exp_formula = tk.StringVar(value="{level} * 500")
        ttk.Entry(base_frame, textvariable=self.level_exp_formula, width=30).grid(row=1, column=1, padx=5, pady=2)
        
        # 特殊等级奖励
        special_frame = ttk.LabelFrame(scrollable_frame, text="特殊等级奖励")
        special_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 特殊等级列表
        self.special_levels_tree = ttk.Treeview(special_frame, columns=("level", "gold", "exp", "items"), show="headings", height=6)
        self.special_levels_tree.heading("level", text="等级")
        self.special_levels_tree.heading("gold", text="金币")
        self.special_levels_tree.heading("exp", text="经验")
        self.special_levels_tree.heading("items", text="物品数量")
        
        self.special_levels_tree.column("level", width=80)
        self.special_levels_tree.column("gold", width=100)
        self.special_levels_tree.column("exp", width=100)
        self.special_levels_tree.column("items", width=100)
        
        self.special_levels_tree.pack(fill=tk.X, padx=5, pady=5)
        
        # 特殊等级操作按钮
        special_btn_frame = ttk.Frame(special_frame)
        special_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(special_btn_frame, text="添加特殊等级", command=self.add_special_level).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(special_btn_frame, text="编辑选中", command=self.edit_special_level).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(special_btn_frame, text="删除选中", command=self.delete_special_level).pack(side=tk.LEFT)
        
        # 邮件模板
        mail_frame = ttk.LabelFrame(scrollable_frame, text="邮件模板")
        mail_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(mail_frame, text="邮件标题:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.level_mail_title = tk.StringVar(value="恭喜升级到{level}级！")
        ttk.Entry(mail_frame, textvariable=self.level_mail_title, width=50).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(mail_frame, text="邮件内容:").grid(row=1, column=0, sticky=tk.NW, padx=5, pady=2)
        self.level_mail_content = tk.Text(mail_frame, width=50, height=4)
        self.level_mail_content.grid(row=1, column=1, padx=5, pady=2)
        self.level_mail_content.insert(tk.END, "恭喜{player_name}升级到{level}级！\n\n作为奖励，我们为您准备了丰厚的升级礼包！")
        
        ttk.Label(mail_frame, text="过期天数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.level_expire_days = tk.IntVar(value=7)
        ttk.Spinbox(mail_frame, from_=1, to=30, textvariable=self.level_expire_days, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.level_up_frame = scrollable_frame
    
    def create_welcome_editor(self):
        """创建欢迎奖励编辑器"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="欢迎奖励")
        
        # 奖励设置
        reward_frame = ttk.LabelFrame(frame, text="奖励设置")
        reward_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(reward_frame, text="金币:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.welcome_gold = tk.IntVar(value=10000)
        ttk.Spinbox(reward_frame, from_=0, to=100000, textvariable=self.welcome_gold, width=15).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(reward_frame, text="经验:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.welcome_exp = tk.IntVar(value=1000)
        ttk.Spinbox(reward_frame, from_=0, to=50000, textvariable=self.welcome_exp, width=15).grid(row=1, column=1, padx=5, pady=5)
        
        # 物品奖励
        items_frame = ttk.LabelFrame(frame, text="物品奖励")
        items_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.welcome_items_tree = ttk.Treeview(items_frame, columns=("id", "name", "quantity"), show="headings", height=8)
        self.welcome_items_tree.heading("id", text="物品ID")
        self.welcome_items_tree.heading("name", text="物品名称")
        self.welcome_items_tree.heading("quantity", text="数量")
        
        self.welcome_items_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 物品操作按钮
        items_btn_frame = ttk.Frame(items_frame)
        items_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(items_btn_frame, text="添加物品", command=self.add_welcome_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(items_btn_frame, text="编辑选中", command=self.edit_welcome_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(items_btn_frame, text="删除选中", command=self.delete_welcome_item).pack(side=tk.LEFT)
        
        # 邮件模板
        mail_frame = ttk.LabelFrame(frame, text="邮件模板")
        mail_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(mail_frame, text="邮件标题:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.welcome_mail_title = tk.StringVar(value="欢迎来到游戏世界！")
        ttk.Entry(mail_frame, textvariable=self.welcome_mail_title, width=50).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(mail_frame, text="邮件内容:").grid(row=1, column=0, sticky=tk.NW, padx=5, pady=2)
        self.welcome_mail_content = tk.Text(mail_frame, width=50, height=4)
        self.welcome_mail_content.grid(row=1, column=1, padx=5, pady=2)
        self.welcome_mail_content.insert(tk.END, "欢迎{player_name}加入游戏！\n\n为了帮助您更好地开始冒险，我们为您准备了新手礼包！")
    
    def create_special_editor(self):
        """创建特殊奖励编辑器"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="特殊奖励")
        
        # 特殊奖励列表
        list_frame = ttk.LabelFrame(frame, text="特殊奖励列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.special_rewards_tree = ttk.Treeview(list_frame, columns=("type", "enabled", "gold", "exp", "description"), show="headings", height=10)
        self.special_rewards_tree.heading("type", text="奖励类型")
        self.special_rewards_tree.heading("enabled", text="启用")
        self.special_rewards_tree.heading("gold", text="金币")
        self.special_rewards_tree.heading("exp", text="经验")
        self.special_rewards_tree.heading("description", text="描述")
        
        self.special_rewards_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 特殊奖励操作按钮
        special_btn_frame = ttk.Frame(list_frame)
        special_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(special_btn_frame, text="添加奖励", command=self.add_special_reward).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(special_btn_frame, text="编辑选中", command=self.edit_special_reward).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(special_btn_frame, text="删除选中", command=self.delete_special_reward).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(special_btn_frame, text="启用/禁用", command=self.toggle_special_reward).pack(side=tk.LEFT)
    
    def create_json_editor(self):
        """创建JSON编辑器"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="JSON编辑")
        
        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="格式化JSON", command=self.format_json).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="验证JSON", command=self.validate_json).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="从表单同步", command=self.sync_from_form).pack(side=tk.LEFT)
        
        # JSON文本编辑器
        text_frame = ttk.Frame(frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.json_text = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 10))
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.json_text.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.json_text.xview)
        
        self.json_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.json_text.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
    
    def load_config_list(self):
        """加载配置文件列表"""
        self.config_listbox.delete(0, tk.END)
        
        config_types = self.config_manager.get_all_config_types()
        for config_type in config_types:
            enabled = "✓" if self.config_manager.is_config_enabled(config_type) else "✗"
            version = self.config_manager.get_config_version(config_type)
            display_text = f"{enabled} {config_type} (v{version})"
            self.config_listbox.insert(tk.END, display_text)
    
    def on_config_select(self, event):
        """配置选择事件"""
        selection = self.config_listbox.curselection()
        if not selection:
            return
        
        # 解析选中的配置类型
        selected_text = self.config_listbox.get(selection[0])
        config_type = selected_text.split()[1]  # 获取配置类型名称
        
        self.load_config(config_type)
    
    def load_config(self, config_type: str):
        """加载指定配置"""
        try:
            config_data = self.config_manager.load_config(config_type)
            if not config_data:
                messagebox.showerror("错误", f"无法加载配置: {config_type}")
                return
            
            self.current_config_type = config_type
            self.current_config_data = config_data.copy()
            
            self.config_title_label.config(text=f"编辑配置: {config_type}")
            
            # 根据配置类型加载到对应的编辑器
            if config_type == "level_up":
                self.load_level_up_config(config_data)
                self.notebook.select(0)
            elif config_type == "welcome":
                self.load_welcome_config(config_data)
                self.notebook.select(1)
            elif config_type == "special":
                self.load_special_config(config_data)
                self.notebook.select(2)
            
            # 同时加载到JSON编辑器
            self.load_json_config(config_data)
            
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def load_level_up_config(self, config_data: Dict[str, Any]):
        """加载升级奖励配置到表单"""
        # 基础奖励公式
        base_rewards = config_data.get("base_rewards", {})
        if "gold" in base_rewards:
            self.level_gold_formula.set(base_rewards["gold"].get("formula", "{level} * 1000"))
        if "experience" in base_rewards:
            self.level_exp_formula.set(base_rewards["experience"].get("formula", "{level} * 500"))
        
        # 特殊等级奖励
        self.special_levels_tree.delete(*self.special_levels_tree.get_children())
        special_levels = config_data.get("special_levels", {})
        for level, reward in special_levels.items():
            gold = reward.get("gold", 0)
            exp = reward.get("experience", 0)
            items_count = len(reward.get("items", []))
            self.special_levels_tree.insert("", tk.END, values=(level, gold, exp, items_count))
        
        # 邮件模板
        mail_template = config_data.get("mail_template", {})
        self.level_mail_title.set(mail_template.get("title", "恭喜升级到{level}级！"))
        
        content = mail_template.get("content", "恭喜{player_name}升级到{level}级！")
        self.level_mail_content.delete(1.0, tk.END)
        self.level_mail_content.insert(tk.END, content)
        
        self.level_expire_days.set(mail_template.get("expire_days", 7))
    
    def load_welcome_config(self, config_data: Dict[str, Any]):
        """加载欢迎奖励配置到表单"""
        # 奖励设置
        rewards = config_data.get("rewards", {})
        self.welcome_gold.set(rewards.get("gold", 10000))
        self.welcome_exp.set(rewards.get("experience", 1000))
        
        # 物品奖励
        self.welcome_items_tree.delete(*self.welcome_items_tree.get_children())
        items = rewards.get("items", [])
        for item in items:
            self.welcome_items_tree.insert("", tk.END, values=(item.get("id", ""), item.get("name", ""), item.get("quantity", 1)))
        
        # 邮件模板
        mail_template = config_data.get("mail_template", {})
        self.welcome_mail_title.set(mail_template.get("title", "欢迎来到游戏世界！"))
        
        content = mail_template.get("content", "欢迎{player_name}加入游戏！")
        self.welcome_mail_content.delete(1.0, tk.END)
        self.welcome_mail_content.insert(tk.END, content)
    
    def load_special_config(self, config_data: Dict[str, Any]):
        """加载特殊奖励配置到表单"""
        self.special_rewards_tree.delete(*self.special_rewards_tree.get_children())
        
        rewards = config_data.get("rewards", {})
        for reward_type, reward_data in rewards.items():
            enabled = "是" if reward_data.get("enabled", True) else "否"
            gold = reward_data.get("rewards", {}).get("gold", 0)
            exp = reward_data.get("rewards", {}).get("experience", 0)
            description = reward_data.get("description", "")
            
            self.special_rewards_tree.insert("", tk.END, values=(reward_type, enabled, gold, exp, description))
    
    def load_json_config(self, config_data: Dict[str, Any]):
        """加载配置到JSON编辑器"""
        self.json_text.delete(1.0, tk.END)
        json_str = json.dumps(config_data, ensure_ascii=False, indent=2)
        self.json_text.insert(tk.END, json_str)
    
    def save_config(self):
        """保存配置"""
        if not self.current_config_type:
            messagebox.showwarning("警告", "请先选择要保存的配置")
            return
        
        try:
            # 从当前选中的标签页收集数据
            current_tab = self.notebook.index(self.notebook.select())
            
            if current_tab == 3:  # JSON编辑器
                config_data = self.get_json_config()
            else:
                config_data = self.collect_form_data()
            
            if not config_data:
                return
            
            # 保存配置
            if self.config_manager.save_config(self.current_config_type, config_data):
                messagebox.showinfo("成功", "配置保存成功！")
                self.current_config_data = config_data.copy()
                self.load_config_list()  # 刷新列表
            else:
                messagebox.showerror("错误", "配置保存失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def collect_form_data(self) -> Optional[Dict[str, Any]]:
        """从表单收集配置数据"""
        try:
            if self.current_config_type == "level_up":
                return self.collect_level_up_data()
            elif self.current_config_type == "welcome":
                return self.collect_welcome_data()
            elif self.current_config_type == "special":
                return self.collect_special_data()
            else:
                return None
        except Exception as e:
            messagebox.showerror("错误", f"收集表单数据失败: {e}")
            return None
    
    def collect_level_up_data(self) -> Dict[str, Any]:
        """收集升级奖励配置数据"""
        # 基础配置结构
        config_data = {
            "version": "1.0.0",
            "description": "升级奖励配置",
            "base_rewards": {
                "gold": {
                    "formula": self.level_gold_formula.get(),
                    "description": "基础金币奖励公式"
                },
                "experience": {
                    "formula": self.level_exp_formula.get(),
                    "description": "基础经验奖励公式"
                }
            },
            "special_levels": {},
            "mail_template": {
                "title": self.level_mail_title.get(),
                "content": self.level_mail_content.get(1.0, tk.END).strip(),
                "sender": "系统",
                "expire_days": self.level_expire_days.get()
            }
        }
        
        # 收集特殊等级奖励
        for item in self.special_levels_tree.get_children():
            values = self.special_levels_tree.item(item, "values")
            level = values[0]
            gold = int(values[1]) if values[1] else 0
            exp = int(values[2]) if values[2] else 0
            
            config_data["special_levels"][level] = {
                "gold": gold,
                "experience": exp,
                "items": []  # 这里可以扩展物品奖励
            }
        
        return config_data
    
    def collect_welcome_data(self) -> Dict[str, Any]:
        """收集欢迎奖励配置数据"""
        # 收集物品列表
        items = []
        for item in self.welcome_items_tree.get_children():
            values = self.welcome_items_tree.item(item, "values")
            items.append({
                "id": values[0],
                "name": values[1],
                "quantity": int(values[2]) if values[2] else 1
            })
        
        config_data = {
            "version": "1.0.0",
            "description": "欢迎奖励配置",
            "rewards": {
                "gold": self.welcome_gold.get(),
                "experience": self.welcome_exp.get(),
                "items": items
            },
            "conditions": {
                "first_login": True,
                "player_level": {"max": 1},
                "once_only": True
            },
            "mail_template": {
                "title": self.welcome_mail_title.get(),
                "content": self.welcome_mail_content.get(1.0, tk.END).strip(),
                "sender": "系统",
                "expire_days": 7
            }
        }
        
        return config_data
    
    def collect_special_data(self) -> Dict[str, Any]:
        """收集特殊奖励配置数据"""
        # 这里返回当前的配置数据，因为特殊奖励的编辑比较复杂
        # 在实际应用中可以实现更详细的编辑功能
        return self.current_config_data
    
    def get_json_config(self) -> Optional[Dict[str, Any]]:
        """从JSON编辑器获取配置"""
        try:
            json_str = self.json_text.get(1.0, tk.END).strip()
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            messagebox.showerror("JSON错误", f"JSON格式错误: {e}")
            return None
    
    def reset_config(self):
        """重置配置"""
        if not self.current_config_data:
            return
        
        if messagebox.askyesno("确认", "确定要重置到上次保存的状态吗？"):
            self.load_config(self.current_config_type)
    
    def test_config(self):
        """测试配置"""
        if not self.current_config_type:
            messagebox.showwarning("警告", "请先选择配置")
            return
        
        try:
            # 测试配置的有效性
            test_data = {"player_name": "测试玩家", "level": 5, "player_id": "test_player"}
            
            if self.current_config_type == "level_up":
                result = self.reward_service.calculate_level_up_reward(5, test_data)
            elif self.current_config_type == "welcome":
                result = self.reward_service.calculate_welcome_reward(test_data)
            else:
                messagebox.showinfo("测试", "该配置类型暂不支持测试")
                return
            
            if result.get("success"):
                rewards = result["rewards"]
                mail = result["mail"]
                
                test_result = f"测试成功！\n\n奖励内容：\n金币: {rewards.get('gold', 0)}\n经验: {rewards.get('experience', 0)}\n物品数量: {len(rewards.get('items', []))}\n\n邮件标题: {mail.get('title', '')}\n邮件内容: {mail.get('content', '')[:100]}..."
                
                messagebox.showinfo("测试结果", test_result)
            else:
                messagebox.showerror("测试失败", f"配置测试失败: {result.get('reason', '未知错误')}")
                
        except Exception as e:
            messagebox.showerror("测试错误", f"测试过程中发生错误: {e}")
    
    def format_json(self):
        """格式化JSON"""
        try:
            json_str = self.json_text.get(1.0, tk.END).strip()
            data = json.loads(json_str)
            formatted = json.dumps(data, ensure_ascii=False, indent=2)
            
            self.json_text.delete(1.0, tk.END)
            self.json_text.insert(tk.END, formatted)
            
            messagebox.showinfo("成功", "JSON格式化完成")
        except json.JSONDecodeError as e:
            messagebox.showerror("错误", f"JSON格式错误: {e}")
    
    def validate_json(self):
        """验证JSON"""
        try:
            json_str = self.json_text.get(1.0, tk.END).strip()
            json.loads(json_str)
            messagebox.showinfo("验证成功", "JSON格式正确")
        except json.JSONDecodeError as e:
            messagebox.showerror("验证失败", f"JSON格式错误: {e}")
    
    def sync_from_form(self):
        """从表单同步到JSON编辑器"""
        config_data = self.collect_form_data()
        if config_data:
            self.load_json_config(config_data)
            messagebox.showinfo("成功", "已从表单同步到JSON编辑器")
    
    # 以下是各种添加/编辑/删除操作的占位方法
    # 在实际应用中需要实现详细的编辑对话框
    
    def add_special_level(self):
        messagebox.showinfo("提示", "添加特殊等级功能待实现")
    
    def edit_special_level(self):
        messagebox.showinfo("提示", "编辑特殊等级功能待实现")
    
    def delete_special_level(self):
        selection = self.special_levels_tree.selection()
        if selection:
            self.special_levels_tree.delete(selection[0])
    
    def add_welcome_item(self):
        messagebox.showinfo("提示", "添加欢迎物品功能待实现")
    
    def edit_welcome_item(self):
        messagebox.showinfo("提示", "编辑欢迎物品功能待实现")
    
    def delete_welcome_item(self):
        selection = self.welcome_items_tree.selection()
        if selection:
            self.welcome_items_tree.delete(selection[0])
    
    def add_special_reward(self):
        messagebox.showinfo("提示", "添加特殊奖励功能待实现")
    
    def edit_special_reward(self):
        messagebox.showinfo("提示", "编辑特殊奖励功能待实现")
    
    def delete_special_reward(self):
        selection = self.special_rewards_tree.selection()
        if selection:
            self.special_rewards_tree.delete(selection[0])
    
    def toggle_special_reward(self):
        messagebox.showinfo("提示", "切换特殊奖励状态功能待实现")
    
    def create_new_config(self):
        messagebox.showinfo("提示", "创建新配置功能待实现")
    
    def import_config(self):
        filename = filedialog.askopenfilename(
            title="导入配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            messagebox.showinfo("提示", f"导入配置功能待实现: {filename}")
    
    def export_config(self):
        if not self.current_config_type:
            messagebox.showwarning("警告", "请先选择要导出的配置")
            return
        
        filename = filedialog.asksaveasfilename(
            title="导出配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            messagebox.showinfo("提示", f"导出配置功能待实现: {filename}")
    
    def run(self):
        """运行界面"""
        self.master.mainloop()

def main():
    """主函数"""
    try:
        app = RewardConfigGUI()
        app.run()
    except Exception as e:
        print(f"启动配置管理界面失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()