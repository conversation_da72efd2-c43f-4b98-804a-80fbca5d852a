#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云服务连接管理器
负责管理客户端与云端服务器的连接，提供邮件和聊天功能的API接口
"""

import asyncio
import json
import requests
import websockets
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

class CloudConnectionManager:
    """云服务连接管理器"""
    
    def __init__(self):
        self.server_url = "http://117.72.223.160:8000"  # 默认服务器地址
        self.ws_url = "ws://117.72.223.160:8000/ws/chat"
        self.access_token = None
        self.user_info = None
        self.is_connected = False
        self.websocket = None
        self.ws_thread = None
        
        # 回调函数
        self.on_message_received = None
        self.on_connection_status_changed = None
        self.on_mail_notification = None
        
        # 消息缓存
        self.chat_history = []
        self.mail_list = []
        
        # 连接状态监控
        self.connection_check_interval = 30  # 30秒检查一次连接
        self.last_ping_time = 0
        
    def set_server_url(self, url: str):
        """设置服务器地址"""
        self.server_url = url.rstrip('/')
        self.ws_url = url.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws/chat'
    
    def set_callbacks(self, on_message_received: Callable = None, 
                      on_connection_status_changed: Callable = None,
                      on_mail_notification: Callable = None):
        """设置回调函数"""
        self.on_message_received = on_message_received
        self.on_connection_status_changed = on_connection_status_changed
        self.on_mail_notification = on_mail_notification
    
    def connect_to_server(self, username: str, password: str) -> bool:
        """连接到云端服务器"""
        try:
            # 测试服务器连接
            response = requests.get(f"{self.server_url}/", timeout=5)
            if response.status_code != 200:
                self._notify_connection_status(False, "服务器无法访问")
                return False
            
            # 用户登录
            login_data = {"username": username, "password": password}
            response = requests.post(f"{self.server_url}/auth/login", 
                                   json=login_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.user_info = {
                    "user_id": data.get("user_id"),
                    "username": data.get("username")
                }
                self.is_connected = True
                
                # 启动WebSocket连接
                self._start_websocket_connection()
                
                # 开始连接监控
                self._start_connection_monitor()
                
                # 同步邮件和聊天历史
                self._sync_initial_data()
                
                self._notify_connection_status(True, "云端服务器连接成功")
                return True
            else:
                error_msg = response.json().get("detail", "登录失败")
                self._notify_connection_status(False, f"登录失败: {error_msg}")
                return False
                
        except requests.exceptions.RequestException as e:
            self._notify_connection_status(False, f"连接失败: {str(e)}")
            return False
        except Exception as e:
            self._notify_connection_status(False, f"未知错误: {str(e)}")
            return False
    
    def disconnect_from_server(self):
        """断开服务器连接"""
        self.is_connected = False
        self.access_token = None
        self.user_info = None
        
        # 关闭WebSocket连接
        if self.websocket:
            asyncio.create_task(self.websocket.close())
            self.websocket = None
        
        # 停止WebSocket线程
        if self.ws_thread and self.ws_thread.is_alive():
            self.ws_thread.join(timeout=2)
            
        self._notify_connection_status(False, "已断开服务器连接")
    
    def _start_websocket_connection(self):
        """启动WebSocket连接"""
        if self.ws_thread and self.ws_thread.is_alive():
            return
            
        self.ws_thread = threading.Thread(target=self._websocket_worker, daemon=True)
        self.ws_thread.start()
    
    def _websocket_worker(self):
        """WebSocket工作线程"""
        async def websocket_handler():
            try:
                headers = {"Authorization": f"Bearer {self.access_token}"} if self.access_token else {}
                async with websockets.connect(self.ws_url, extra_headers=headers) as websocket:
                    self.websocket = websocket
                    print("✅ WebSocket连接已建立")
                    
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            self._handle_websocket_message(data)
                        except json.JSONDecodeError:
                            print(f"收到无效的WebSocket消息: {message}")
                            
            except Exception as e:
                print(f"WebSocket连接错误: {e}")
                self.websocket = None
        
        # 运行WebSocket连接
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(websocket_handler())
        except Exception as e:
            print(f"WebSocket循环错误: {e}")
        finally:
            loop.close()
    
    def _handle_websocket_message(self, data: dict):
        """处理WebSocket消息"""
        message_type = data.get('type', 'chat')
        
        if message_type == 'chat':
            # 聊天消息
            self.chat_history.append(data)
            if self.on_message_received:
                self.on_message_received(data)
        elif message_type == 'mail_notification':
            # 邮件通知
            if self.on_mail_notification:
                self.on_mail_notification(data)
    
    def _start_connection_monitor(self):
        """启动连接监控"""
        def monitor_worker():
            while self.is_connected:
                try:
                    # 发送心跳检测
                    response = requests.get(f"{self.server_url}/", 
                                          headers=self._get_auth_headers(), 
                                          timeout=5)
                    if response.status_code != 200:
                        self._handle_connection_lost()
                        break
                    
                    self.last_ping_time = time.time()
                    
                except Exception as e:
                    print(f"连接检查失败: {e}")
                    self._handle_connection_lost()
                    break
                
                time.sleep(self.connection_check_interval)
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
    
    def _handle_connection_lost(self):
        """处理连接丢失"""
        self.is_connected = False
        self._notify_connection_status(False, "与服务器的连接已丢失")
    
    def _sync_initial_data(self):
        """同步初始数据"""
        try:
            # 获取聊天历史
            self._load_chat_history()
            
            # 获取邮件列表
            self._load_mail_list()
            
        except Exception as e:
            print(f"同步初始数据失败: {e}")
    
    def _get_auth_headers(self) -> dict:
        """获取认证头"""
        if self.access_token:
            return {"Authorization": f"Bearer {self.access_token}"}
        return {}
    
    def _notify_connection_status(self, connected: bool, message: str):
        """通知连接状态变化"""
        if self.on_connection_status_changed:
            self.on_connection_status_changed(connected, message)
        print(f"连接状态: {'已连接' if connected else '已断开'} - {message}")
    
    # ==================== 聊天功能 ====================
    
    def send_chat_message(self, message: str, channel: str = "global") -> bool:
        """发送聊天消息"""
        if not self.is_connected or not self.websocket or not self.user_info:
            return False
        
        try:
            message_data = {
                "type": "chat",
                "sender_id": self.user_info["user_id"],
                "sender_name": self.user_info["username"],
                "message": message,
                "channel": channel,
                "timestamp": datetime.now().isoformat()
            }
            
            # 通过WebSocket发送消息
            asyncio.create_task(self.websocket.send(json.dumps(message_data)))
            return True
            
        except Exception as e:
            print(f"发送聊天消息失败: {e}")
            return False
    
    def _load_chat_history(self, limit: int = 50):
        """加载聊天历史"""
        try:
            response = requests.get(f"{self.server_url}/chat/history", 
                                  params={"limit": limit},
                                  headers=self._get_auth_headers(),
                                  timeout=10)
            
            if response.status_code == 200:
                self.chat_history = response.json()
                return self.chat_history
            else:
                print(f"加载聊天历史失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"加载聊天历史错误: {e}")
            return []
    
    def get_chat_history(self) -> List[Dict]:
        """获取本地缓存的聊天历史"""
        return self.chat_history
    
    # ==================== 邮件功能 ====================
    
    def _load_mail_list(self):
        """加载邮件列表"""
        try:
            response = requests.get(f"{self.server_url}/mail/list",
                                  headers=self._get_auth_headers(),
                                  timeout=10)
            
            if response.status_code == 200:
                self.mail_list = response.json()
                return self.mail_list
            else:
                print(f"加载邮件列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"加载邮件列表错误: {e}")
            return []
    
    def get_mail_list(self) -> List[Dict]:
        """获取邮件列表"""
        if self.is_connected:
            return self._load_mail_list()
        return []
    
    def get_mail_detail(self, mail_id: str) -> Optional[Dict]:
        """获取邮件详情"""
        if not self.is_connected:
            return None
        
        try:
            response = requests.get(f"{self.server_url}/mail/{mail_id}",
                                  headers=self._get_auth_headers(),
                                  timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"获取邮件详情失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"获取邮件详情错误: {e}")
            return None
    
    def send_mail(self, recipient_id: str, title: str, content: str, 
                  rewards: Dict[str, Any] = None, expire_hours: int = 168) -> bool:
        """发送邮件"""
        if not self.is_connected:
            return False
        
        try:
            mail_data = {
                "recipient_id": recipient_id,
                "title": title,
                "content": content,
                "rewards": rewards,
                "expire_hours": expire_hours
            }
            
            response = requests.post(f"{self.server_url}/mail/send",
                                   json=mail_data,
                                   headers=self._get_auth_headers(),
                                   timeout=10)
            
            if response.status_code == 200:
                # 刷新邮件列表
                self._load_mail_list()
                return True
            else:
                print(f"发送邮件失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"发送邮件错误: {e}")
            return False
    
    def claim_mail_reward(self, mail_id: str) -> Optional[Dict]:
        """领取邮件奖励"""
        if not self.is_connected:
            return None
        
        try:
            action_data = {"action": "claim"}
            response = requests.post(f"{self.server_url}/mail/{mail_id}/action",
                                   json=action_data,
                                   headers=self._get_auth_headers(),
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                # 刷新邮件列表
                self._load_mail_list()
                return result.get("rewards")
            else:
                print(f"领取邮件奖励失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"领取邮件奖励错误: {e}")
            return None
    
    def delete_mail(self, mail_id: str) -> bool:
        """删除邮件"""
        if not self.is_connected:
            return False
        
        try:
            action_data = {"action": "delete"}
            response = requests.post(f"{self.server_url}/mail/{mail_id}/action",
                                   json=action_data,
                                   headers=self._get_auth_headers(),
                                   timeout=10)
            
            if response.status_code == 200:
                # 刷新邮件列表
                self._load_mail_list()
                return True
            else:
                print(f"删除邮件失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"删除邮件错误: {e}")
            return False
    
    # ==================== 状态查询 ====================
    
    def is_server_connected(self) -> bool:
        """检查是否连接到服务器"""
        return self.is_connected
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "is_connected": self.is_connected,
            "server_url": self.server_url,
            "user_info": self.user_info,
            "last_ping_time": self.last_ping_time,
            "chat_history_count": len(self.chat_history),
            "mail_count": len(self.mail_list)
        }

# 全局云连接管理器实例
cloud_manager = CloudConnectionManager()

def get_cloud_manager() -> CloudConnectionManager:
    """获取云连接管理器实例"""
    return cloud_manager 