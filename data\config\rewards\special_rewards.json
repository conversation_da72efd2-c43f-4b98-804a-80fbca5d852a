{"version": "1.0.0", "description": "特殊奖励配置 - 节日活动等", "reward_templates": {"daily_login": {"name": "每日登录奖励", "rewards": {"gold": 500, "experience": 100}, "mail_template": {"title": "每日登录奖励", "content": "感谢您今日的登录！这是您的每日奖励。"}, "conditions": {"trigger_type": "daily_login", "cooldown_hours": 24}}, "weekend_bonus": {"name": "周末奖励", "rewards": {"gold": 2000, "experience": 500, "items": [{"id": "exp_boost_potion", "name": "经验加成药水", "quantity": 3}]}, "mail_template": {"title": "周末特别奖励！", "content": "周末愉快！享受您的特别奖励吧！"}, "conditions": {"trigger_type": "weekend_login", "active_days": ["Saturday", "Sunday"]}}, "festival_gift": {"name": "节日礼包", "rewards": {"gold": 5000, "experience": 1000, "items": [{"id": "festival_decoration", "name": "节日装饰", "quantity": 1}, {"id": "rare_gem", "name": "稀有宝石", "quantity": 2}]}, "mail_template": {"title": "节日快乐！", "content": "节日快乐！感谢您一直以来的支持，这是我们的节日礼物！"}, "conditions": {"trigger_type": "manual", "event_active": true}}, "compensation": {"name": "补偿奖励", "rewards": {"gold": 1000, "experience": 200}, "mail_template": {"title": "系统补偿", "content": "由于系统维护给您带来的不便，我们深表歉意。这是我们的补偿奖励。"}, "conditions": {"trigger_type": "manual"}}}}