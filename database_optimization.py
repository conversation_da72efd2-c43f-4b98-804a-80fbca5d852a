#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库优化模块
实现从SQLite到PostgreSQL的迁移，支持数据分片和读写分离
"""

import asyncio
import asyncpg
import psycopg2
from psycopg2 import pool
import hashlib
import json
import logging
import time
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseRole(Enum):
    """数据库角色枚举"""
    MASTER = "master"  # 主库（写）
    SLAVE = "slave"   # 从库（读）

@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str
    port: int
    database: str
    username: str
    password: str
    role: DatabaseRole
    max_connections: int = 20
    min_connections: int = 5

class PostgreSQLOptimizer:
    """PostgreSQL优化器，支持读写分离和分片"""
    
    def __init__(self, configs: List[DatabaseConfig]):
        """
        初始化数据库优化器
        :param configs: 数据库配置列表
        """
        self.configs = configs
        self.master_pools = {}  # 主库连接池
        self.slave_pools = {}   # 从库连接池
        self.shard_count = 4    # 分片数量
        self.async_pools = {}   # 异步连接池
        
        # 分离主从配置
        self.master_configs = [c for c in configs if c.role == DatabaseRole.MASTER]
        self.slave_configs = [c for c in configs if c.role == DatabaseRole.SLAVE]
        
        # 初始化连接池
        self._init_connection_pools()
        
    def _init_connection_pools(self):
        """初始化连接池"""
        try:
            # 初始化主库连接池
            for config in self.master_configs:
                pool_key = f"{config.host}:{config.port}"
                self.master_pools[pool_key] = psycopg2.pool.ThreadedConnectionPool(
                    minconn=config.min_connections,
                    maxconn=config.max_connections,
                    host=config.host,
                    port=config.port,
                    database=config.database,
                    user=config.username,
                    password=config.password,
                    # 优化参数
                    options="-c shared_preload_libraries=pg_stat_statements"
                )
                logger.info(f"主库连接池初始化成功: {pool_key}")
            
            # 初始化从库连接池
            for config in self.slave_configs:
                pool_key = f"{config.host}:{config.port}"
                self.slave_pools[pool_key] = psycopg2.pool.ThreadedConnectionPool(
                    minconn=config.min_connections,
                    maxconn=config.max_connections,
                    host=config.host,
                    port=config.port,
                    database=config.database,
                    user=config.username,
                    password=config.password
                )
                logger.info(f"从库连接池初始化成功: {pool_key}")
                
        except Exception as e:
            logger.error(f"连接池初始化失败: {e}")
            raise
    
    async def _init_async_pools(self):
        """初始化异步连接池"""
        for config in self.configs:
            pool_key = f"{config.host}:{config.port}"
            try:
                self.async_pools[pool_key] = await asyncpg.create_pool(
                    host=config.host,
                    port=config.port,
                    database=config.database,
                    user=config.username,
                    password=config.password,
                    min_size=config.min_connections,
                    max_size=config.max_connections,
                    command_timeout=60
                )
                logger.info(f"异步连接池初始化成功: {pool_key}")
            except Exception as e:
                logger.error(f"异步连接池初始化失败 {pool_key}: {e}")
    
    def _get_shard_key(self, identifier: str) -> int:
        """
        获取分片键
        :param identifier: 标识符（如用户ID、玩家ID）
        :return: 分片索引
        """
        hash_value = hashlib.md5(identifier.encode()).hexdigest()
        return int(hash_value, 16) % self.shard_count
    
    def _get_table_name(self, base_table: str, shard_key: int) -> str:
        """
        获取分片表名
        :param base_table: 基础表名
        :param shard_key: 分片键
        :return: 分片表名
        """
        return f"{base_table}_shard_{shard_key}"
    
    def get_connection(self, for_write: bool = False, shard_key: Optional[int] = None):
        """
        获取数据库连接
        :param for_write: 是否用于写操作
        :param shard_key: 分片键
        :return: 数据库连接
        """
        try:
            if for_write:
                # 写操作使用主库
                if not self.master_pools:
                    raise Exception("没有可用的主库连接池")
                pool = list(self.master_pools.values())[0]  # 简化版本，使用第一个主库
            else:
                # 读操作使用从库，如果没有从库则使用主库
                if self.slave_pools:
                    if shard_key is not None:
                        # 根据分片键选择从库
                        pool_keys = list(self.slave_pools.keys())
                        selected_pool_key = pool_keys[shard_key % len(pool_keys)]
                        pool = self.slave_pools[selected_pool_key]
                    else:
                        # 随机选择一个从库
                        pool = list(self.slave_pools.values())[0]
                else:
                    # 没有从库，使用主库
                    pool = list(self.master_pools.values())[0]
            
            return pool.getconn()
            
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
    
    def return_connection(self, conn, for_write: bool = False):
        """
        归还数据库连接
        :param conn: 数据库连接
        :param for_write: 是否为写连接
        """
        try:
            if for_write:
                list(self.master_pools.values())[0].putconn(conn)
            else:
                if self.slave_pools:
                    list(self.slave_pools.values())[0].putconn(conn)
                else:
                    list(self.master_pools.values())[0].putconn(conn)
        except Exception as e:
            logger.error(f"归还数据库连接失败: {e}")
    
    @asynccontextmanager
    async def get_async_connection(self, for_write: bool = False):
        """
        异步获取数据库连接
        :param for_write: 是否用于写操作
        """
        pool_key = None
        conn = None
        try:
            if for_write:
                # 写操作使用主库
                master_keys = [k for k, v in self.async_pools.items() 
                              if any(c.role == DatabaseRole.MASTER 
                                   for c in self.configs 
                                   if f"{c.host}:{c.port}" == k)]
                if not master_keys:
                    raise Exception("没有可用的异步主库连接池")
                pool_key = master_keys[0]
            else:
                # 读操作优先使用从库
                slave_keys = [k for k, v in self.async_pools.items() 
                             if any(c.role == DatabaseRole.SLAVE 
                                  for c in self.configs 
                                  if f"{c.host}:{c.port}" == k)]
                if slave_keys:
                    pool_key = slave_keys[0]
                else:
                    # 没有从库，使用主库
                    master_keys = [k for k, v in self.async_pools.items() 
                                  if any(c.role == DatabaseRole.MASTER 
                                       for c in self.configs 
                                       if f"{c.host}:{c.port}" == k)]
                    pool_key = master_keys[0] if master_keys else None
            
            if not pool_key or pool_key not in self.async_pools:
                raise Exception("没有可用的异步连接池")
            
            pool = self.async_pools[pool_key]
            conn = await pool.acquire()
            yield conn
            
        finally:
            if conn and pool_key and pool_key in self.async_pools:
                await self.async_pools[pool_key].release(conn)

class ShardedGameDatabase:
    """分片游戏数据库"""
    
    def __init__(self, optimizer: PostgreSQLOptimizer):
        self.optimizer = optimizer
        
    def create_tables(self):
        """创建分片表"""
        base_tables = [
            "players", "game_saves", "user_data", 
            "equipment", "inventory", "skills"
        ]
        
        for shard_idx in range(self.optimizer.shard_count):
            conn = None
            try:
                conn = self.optimizer.get_connection(for_write=True)
                cursor = conn.cursor()
                
                for base_table in base_tables:
                    table_name = self.optimizer._get_table_name(base_table, shard_idx)
                    
                    if base_table == "players":
                        sql = f"""
                        CREATE TABLE IF NOT EXISTS {table_name} (
                            player_id VARCHAR(50) PRIMARY KEY,
                            username VARCHAR(50) NOT NULL,
                            data JSONB NOT NULL,
                            level INTEGER DEFAULT 1,
                            experience BIGINT DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                        CREATE INDEX IF NOT EXISTS idx_{table_name}_level ON {table_name}(level);
                        CREATE INDEX IF NOT EXISTS idx_{table_name}_username ON {table_name}(username);
                        """
                    elif base_table == "equipment":
                        sql = f"""
                        CREATE TABLE IF NOT EXISTS {table_name} (
                            equipment_id VARCHAR(50) PRIMARY KEY,
                            player_id VARCHAR(50) NOT NULL,
                            equipment_data JSONB NOT NULL,
                            equipped BOOLEAN DEFAULT FALSE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                        CREATE INDEX IF NOT EXISTS idx_{table_name}_player ON {table_name}(player_id);
                        CREATE INDEX IF NOT EXISTS idx_{table_name}_equipped ON {table_name}(equipped);
                        """
                    # 添加其他表的创建语句...
                    
                    cursor.execute(sql)
                
                conn.commit()
                logger.info(f"分片 {shard_idx} 表创建成功")
                
            except Exception as e:
                logger.error(f"创建分片表失败: {e}")
                if conn:
                    conn.rollback()
            finally:
                if conn:
                    self.optimizer.return_connection(conn, for_write=True)
    
    def save_player_data(self, player_id: str, data: Dict[str, Any]) -> bool:
        """
        保存玩家数据（分片）
        :param player_id: 玩家ID
        :param data: 玩家数据
        :return: 是否成功
        """
        shard_key = self.optimizer._get_shard_key(player_id)
        table_name = self.optimizer._get_table_name("players", shard_key)
        
        conn = None
        try:
            conn = self.optimizer.get_connection(for_write=True, shard_key=shard_key)
            cursor = conn.cursor()
            
            sql = f"""
            INSERT INTO {table_name} (player_id, username, data, level, experience, updated_at)
            VALUES (%(player_id)s, %(username)s, %(data)s, %(level)s, %(experience)s, CURRENT_TIMESTAMP)
            ON CONFLICT (player_id) 
            DO UPDATE SET 
                data = EXCLUDED.data,
                level = EXCLUDED.level,
                experience = EXCLUDED.experience,
                updated_at = CURRENT_TIMESTAMP
            """
            
            cursor.execute(sql, {
                'player_id': player_id,
                'username': data.get('name', ''),
                'data': json.dumps(data),
                'level': data.get('level', 1),
                'experience': data.get('experience', 0)
            })
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"保存玩家数据失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.optimizer.return_connection(conn, for_write=True)
    
    def load_player_data(self, player_id: str) -> Optional[Dict[str, Any]]:
        """
        加载玩家数据（分片）
        :param player_id: 玩家ID
        :return: 玩家数据
        """
        shard_key = self.optimizer._get_shard_key(player_id)
        table_name = self.optimizer._get_table_name("players", shard_key)
        
        conn = None
        try:
            conn = self.optimizer.get_connection(for_write=False, shard_key=shard_key)
            cursor = conn.cursor()
            
            sql = f"SELECT data FROM {table_name} WHERE player_id = %s"
            cursor.execute(sql, (player_id,))
            
            result = cursor.fetchone()
            if result:
                return json.loads(result[0])
            return None
            
        except Exception as e:
            logger.error(f"加载玩家数据失败: {e}")
            return None
        finally:
            if conn:
                self.optimizer.return_connection(conn, for_write=False)

# 数据库配置示例
def get_database_configs() -> List[DatabaseConfig]:
    """获取数据库配置"""
    return [
        # 主库配置
        DatabaseConfig(
            host="localhost",
            port=5432,
            database="game_master",
            username="game_user",
            password="game_password",
            role=DatabaseRole.MASTER,
            max_connections=20
        ),
        # 从库配置
        DatabaseConfig(
            host="localhost",
            port=5433,
            database="game_slave",
            username="game_user",
            password="game_password",
            role=DatabaseRole.SLAVE,
            max_connections=15
        )
    ]

# 使用示例
async def example_usage():
    """使用示例"""
    configs = get_database_configs()
    optimizer = PostgreSQLOptimizer(configs)
    await optimizer._init_async_pools()
    
    # 创建分片数据库
    sharded_db = ShardedGameDatabase(optimizer)
    sharded_db.create_tables()
    
    # 保存玩家数据
    player_data = {
        "name": "测试玩家",
        "level": 10,
        "experience": 1000,
        "class": "warrior"
    }
    
    success = sharded_db.save_player_data("player_001", player_data)
    print(f"保存结果: {success}")
    
    # 加载玩家数据
    loaded_data = sharded_db.load_player_data("player_001")
    print(f"加载结果: {loaded_data}")

if __name__ == "__main__":
    asyncio.run(example_usage()) 