{"skill_books": {"战士": [{"name": "基础剑术", "type": "技能书", "level_required": 7, "description": "学习基础剑术技能的书籍", "skill_id": "basic_sword", "effect": "基础剑术，提高攻击力"}, {"name": "攻杀剑术", "type": "技能书", "level_required": 19, "description": "学习攻杀剑术技能的书籍", "skill_id": "basic_sword", "effect": "基础剑术，提高攻击力"}, {"name": "半月弯刀", "type": "技能书", "level_required": 15, "description": "学习半月弯刀技能的书籍", "skill_id": "half_moon", "effect": "范围攻击技能，可攻击前方多个敌人"}, {"name": "野蛮冲撞", "type": "技能书", "level_required": 30, "description": "学习野蛮冲撞技能的书籍", "skill_id": "rush", "effect": "冲锋技能，可快速靠近敌人并造成伤害"}, {"name": "烈火剑法", "type": "技能书", "level_required": 35, "description": "学习烈火剑法技能的书籍", "skill_id": "fire_sword", "effect": "单体高伤害技能，附带火属性伤害"}, {"name": "刺杀剑术", "type": "技能书", "level_required": 25, "description": "学习刺杀剑术技能的书籍", "skill_id": "assassination", "effect": "单体攻击技能，有几率造成致命一击"}], "法师": [{"name": "火球术", "type": "技能书", "level_required": 7, "description": "学习火球术技能的书籍", "skill_id": "fireball", "effect": "发射一枚火球攻击目标"}, {"name": "雷电术", "type": "技能书", "level_required": 17, "description": "学习雷电术技能的书籍", "skill_id": "lightning", "effect": "召唤雷电打击敌人"}, {"name": "火墙", "type": "技能书", "level_required": 24, "description": "学习火墙技能的书籍", "skill_id": "fire_wall", "effect": "创造一道火墙，对经过的敌人造成伤害"}, {"name": "冰咆哮", "type": "技能书", "level_required": 20, "description": "学习冰咆哮技能的书籍", "skill_id": "ice_roar", "effect": "范围冰系攻击，可以冻结敌人"}, {"name": "魔法盾", "type": "技能书", "level_required": 31, "description": "学习魔法盾技能的书籍", "skill_id": "magic_shield", "effect": "创造魔法护盾减少受到的伤害"}, {"name": "诱惑之光", "type": "技能书", "level_required": 13, "description": "学习诱惑之光技能的书籍", "skill_id": "charm", "effect": "诱惑敌人，使其成为自己的召唤物"}, {"name": "地狱雷光", "type": "技能书", "level_required": 30, "description": "学习地狱雷光技能的书籍", "skill_id": "hell_lightning", "effect": "范围雷系攻击，对多个敌人造成伤害"}], "道士": [{"name": "灵魂火符", "type": "技能书", "level_required": 18, "description": "学习灵魂火符技能的书籍", "skill_id": "soul_fire", "effect": "攻击技能，造成额外灵魂伤害"}, {"name": "隐身术", "type": "技能书", "level_required": 20, "description": "学习隐身术技能的书籍", "skill_id": "invisibility", "effect": "辅助技能，使自己隐身"}, {"name": "圣灵防御术", "type": "技能书", "level_required": 22, "description": "学习圣灵防御术技能的书籍", "skill_id": "holy_armor", "effect": "防御技能，增加物理防御力"}, {"name": "施毒术", "type": "技能书", "level_required": 13, "description": "学习施毒术技能的书籍", "skill_id": "poison", "effect": "攻击技能，使敌人中毒"}, {"name": "召唤骷髅", "type": "技能书", "level_required": 19, "description": "学习召唤骷髅技能的书籍", "skill_id": "summon_skeleton", "effect": "召唤骷髅战士，协助战斗"}, {"name": "召唤神兽", "type": "技能书", "level_required": 26, "description": "学习召唤神兽技能的书籍", "skill_id": "summon_beast", "effect": "召唤一只强大的神兽，拥有高攻击和防御"}, {"name": "治愈术", "type": "技能书", "level_required": 7, "description": "学习治愈术技能的书籍", "skill_id": "healing", "effect": "恢复一定量的生命值"}, {"name": "精神力战法", "type": "技能书", "level_required": 9, "description": "学习精神力战法技能的书籍", "skill_id": "spirit_sword", "effect": "攻击技能，使用精神力伤害敌人"}]}}