#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池管理器
提供安全、高效的数据库连接管理
"""

import sqlite3
import threading
import time
import queue
import logging
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from dataclasses import dataclass

@dataclass
class ConnectionInfo:
    """连接信息"""
    connection: sqlite3.Connection
    created_time: float
    last_used: float
    in_use: bool = False
    use_count: int = 0

class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, 
                 database_path: str,
                 min_connections: int = 2,
                 max_connections: int = 10,
                 max_idle_time: int = 300,  # 5分钟
                 connection_timeout: int = 30):
        """
        初始化连接池
        
        Args:
            database_path: 数据库文件路径
            min_connections: 最小连接数
            max_connections: 最大连接数
            max_idle_time: 最大空闲时间（秒）
            connection_timeout: 获取连接超时时间（秒）
        """
        self.database_path = database_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.max_idle_time = max_idle_time
        self.connection_timeout = connection_timeout
        
        # 连接池
        self.available_connections = queue.Queue(maxsize=max_connections)
        self.all_connections: Dict[int, ConnectionInfo] = {}
        self.connection_counter = 0
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'created_connections': 0,
            'closed_connections': 0,
            'active_connections': 0,
            'total_requests': 0,
            'failed_requests': 0,
            'pool_hits': 0,
            'pool_misses': 0
        }
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化连接池
        self._initialize_pool()
        
        # 启动清理任务
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def _initialize_pool(self):
        """初始化连接池"""
        with self.lock:
            for _ in range(self.min_connections):
                try:
                    conn_info = self._create_connection()
                    if conn_info:
                        self.available_connections.put(conn_info)
                except Exception as e:
                    self.logger.error(f"初始化连接池失败: {e}")
    
    def _create_connection(self) -> Optional[ConnectionInfo]:
        """创建新的数据库连接"""
        try:
            # 创建连接
            conn = sqlite3.connect(
                self.database_path,
                check_same_thread=False,  # 允许多线程使用
                timeout=self.connection_timeout
            )
            
            # 设置连接参数
            conn.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
            conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
            conn.execute("PRAGMA cache_size=10000")  # 增加缓存大小
            conn.execute("PRAGMA temp_store=MEMORY")  # 临时表存储在内存中
            
            # 创建连接信息
            self.connection_counter += 1
            conn_id = self.connection_counter
            current_time = time.time()
            
            conn_info = ConnectionInfo(
                connection=conn,
                created_time=current_time,
                last_used=current_time
            )
            
            self.all_connections[conn_id] = conn_info
            self.stats['created_connections'] += 1
            
            self.logger.debug(f"创建新数据库连接: {conn_id}")
            return conn_info
            
        except Exception as e:
            self.logger.error(f"创建数据库连接失败: {e}")
            self.stats['failed_requests'] += 1
            return None
    
    def _validate_connection(self, conn_info: ConnectionInfo) -> bool:
        """验证连接是否有效"""
        try:
            # 执行简单查询测试连接
            conn_info.connection.execute("SELECT 1").fetchone()
            return True
        except Exception as e:
            self.logger.warning(f"连接验证失败: {e}")
            return False
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn_info = None
        try:
            conn_info = self._get_connection()
            if conn_info:
                yield conn_info.connection
            else:
                raise Exception("无法获取数据库连接")
        finally:
            if conn_info:
                self._return_connection(conn_info)
    
    def _get_connection(self) -> Optional[ConnectionInfo]:
        """从连接池获取连接"""
        self.stats['total_requests'] += 1
        
        try:
            # 尝试从池中获取连接
            conn_info = self.available_connections.get(timeout=self.connection_timeout)
            
            # 验证连接有效性
            if self._validate_connection(conn_info):
                conn_info.in_use = True
                conn_info.last_used = time.time()
                conn_info.use_count += 1
                self.stats['pool_hits'] += 1
                self.stats['active_connections'] += 1
                return conn_info
            else:
                # 连接无效，关闭并创建新连接
                self._close_connection(conn_info)
                return self._create_new_connection()
                
        except queue.Empty:
            # 池中没有可用连接，尝试创建新连接
            return self._create_new_connection()
    
    def _create_new_connection(self) -> Optional[ConnectionInfo]:
        """创建新连接（当池中无可用连接时）"""
        with self.lock:
            if len(self.all_connections) < self.max_connections:
                conn_info = self._create_connection()
                if conn_info:
                    conn_info.in_use = True
                    conn_info.use_count += 1
                    self.stats['pool_misses'] += 1
                    self.stats['active_connections'] += 1
                    return conn_info
            
        self.logger.warning("达到最大连接数限制，无法创建新连接")
        self.stats['failed_requests'] += 1
        return None
    
    def _return_connection(self, conn_info: ConnectionInfo):
        """归还连接到池中"""
        with self.lock:
            if conn_info.in_use:
                conn_info.in_use = False
                conn_info.last_used = time.time()
                self.stats['active_connections'] -= 1
                
                # 检查连接是否仍然有效
                if self._validate_connection(conn_info):
                    try:
                        self.available_connections.put_nowait(conn_info)
                    except queue.Full:
                        # 池已满，关闭连接
                        self._close_connection(conn_info)
                else:
                    # 连接无效，关闭它
                    self._close_connection(conn_info)
    
    def _close_connection(self, conn_info: ConnectionInfo):
        """关闭连接"""
        try:
            conn_info.connection.close()
            # 从连接字典中移除
            for conn_id, info in list(self.all_connections.items()):
                if info is conn_info:
                    del self.all_connections[conn_id]
                    break
            self.stats['closed_connections'] += 1
            self.logger.debug("数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭数据库连接失败: {e}")
    
    def _cleanup_loop(self):
        """清理过期连接的循环"""
        while True:
            try:
                time.sleep(60)  # 每分钟检查一次
                self._cleanup_idle_connections()
            except Exception as e:
                self.logger.error(f"连接清理任务异常: {e}")
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        connections_to_close = []
        
        with self.lock:
            # 找出需要清理的连接
            for conn_id, conn_info in self.all_connections.items():
                if (not conn_info.in_use and 
                    current_time - conn_info.last_used > self.max_idle_time and
                    len(self.all_connections) > self.min_connections):
                    connections_to_close.append(conn_info)
            
            # 关闭过期连接
            for conn_info in connections_to_close:
                try:
                    # 从可用连接队列中移除（如果存在）
                    temp_queue = queue.Queue()
                    while not self.available_connections.empty():
                        try:
                            item = self.available_connections.get_nowait()
                            if item is not conn_info:
                                temp_queue.put(item)
                        except queue.Empty:
                            break
                    
                    # 重新填充队列
                    while not temp_queue.empty():
                        self.available_connections.put(temp_queue.get())
                    
                    # 关闭连接
                    self._close_connection(conn_info)
                    
                except Exception as e:
                    self.logger.error(f"清理连接时出错: {e}")
        
        if connections_to_close:
            self.logger.info(f"清理了 {len(connections_to_close)} 个空闲连接")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self.lock:
            return {
                **self.stats,
                'pool_size': len(self.all_connections),
                'available_connections': self.available_connections.qsize(),
                'active_connections': self.stats['active_connections'],
                'hit_rate': (self.stats['pool_hits'] / max(1, self.stats['total_requests'])) * 100
            }
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            # 清空可用连接队列
            while not self.available_connections.empty():
                try:
                    conn_info = self.available_connections.get_nowait()
                    self._close_connection(conn_info)
                except queue.Empty:
                    break
            
            # 关闭所有连接
            for conn_info in list(self.all_connections.values()):
                if not conn_info.in_use:
                    self._close_connection(conn_info)
        
        self.logger.info("所有数据库连接已关闭")

# 全局连接池实例
_connection_pools: Dict[str, DatabaseConnectionPool] = {}
_pool_lock = threading.Lock()

def get_connection_pool(database_path: str, **kwargs) -> DatabaseConnectionPool:
    """获取数据库连接池实例（单例模式）"""
    with _pool_lock:
        if database_path not in _connection_pools:
            _connection_pools[database_path] = DatabaseConnectionPool(database_path, **kwargs)
        return _connection_pools[database_path]

def close_all_pools():
    """关闭所有连接池"""
    with _pool_lock:
        for pool in _connection_pools.values():
            pool.close_all()
        _connection_pools.clear()

if __name__ == "__main__":
    # 测试连接池
    import tempfile
    import os
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(delete=False, suffix='.db') as f:
        test_db_path = f.name
    
    try:
        # 创建连接池
        pool = DatabaseConnectionPool(test_db_path, min_connections=2, max_connections=5)
        
        # 测试连接获取
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)")
            cursor.execute("INSERT INTO test (name) VALUES (?)", ("测试数据",))
            conn.commit()
            
            cursor.execute("SELECT * FROM test")
            results = cursor.fetchall()
            print(f"查询结果: {results}")
        
        # 显示统计信息
        stats = pool.get_stats()
        print(f"连接池统计: {stats}")
        
        # 关闭连接池
        pool.close_all()
        
    finally:
        # 清理临时文件
        if os.path.exists(test_db_path):
            os.unlink(test_db_path)
    
    print("连接池测试完成")