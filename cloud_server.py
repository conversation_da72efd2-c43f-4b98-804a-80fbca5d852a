#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端游戏服务器
实现邮件系统和聊天系统的REST API接口
支持PostgreSQL数据库和实时WebSocket通信
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

import asyncpg
from fastapi import FastAPI, HTTPException, WebSocket, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel
import redis.asyncio as redis
import jwt
from contextlib import asynccontextmanager

from database_optimization import PostgreSQLOptimizer, DatabaseConfig, DatabaseRole

# 配置常量
SECRET_KEY = "LegendOfMir2024CloudServer"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 数据模型
class UserLogin(BaseModel):
    username: str
    password: str

class ChatMessage(BaseModel):
    sender_id: str
    message: str
    channel: str = "global"

class MailCreate(BaseModel):
    recipient_id: str
    title: str
    content: str
    rewards: Optional[Dict[str, Any]] = None
    expire_hours: int = 168

class MailAction(BaseModel):
    action: str  # "read", "claim", "delete"

# 云端服务器类
class CloudGameServer:
    """云端游戏服务器"""
    
    def __init__(self):
        self.app = FastAPI(title="传奇游戏云端服务器", version="1.0.0")
        self.redis_client = None
        self.db_optimizer = None
        self.connected_clients = {}  # WebSocket连接管理
        self.chat_channels = {"global": set()}  # 聊天频道管理
        
        # 配置CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 注册路由
        self._register_routes()
    
    async def initialize(self):
        """初始化服务器"""
        # 初始化Redis
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        
        # 初始化数据库
        configs = self._get_database_configs()
        self.db_optimizer = PostgreSQLOptimizer(configs)
        await self.db_optimizer._init_async_pools()
        
        # 创建必要的表
        await self._create_tables()
        
        print("✅ 云端服务器初始化完成")
    
    def _get_database_configs(self) -> List[DatabaseConfig]:
        """获取数据库配置"""
        return [
            DatabaseConfig(
                host="localhost",
                port=5432,
                database="game_cloud",
                username="game_user",
                password="game_password",
                role=DatabaseRole.MASTER,
                max_connections=20
            )
        ]
    
    async def _create_tables(self):
        """创建数据库表"""
        async with self.db_optimizer.get_async_connection(for_write=True) as conn:
            # 创建用户表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id VARCHAR(50) PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    is_online BOOLEAN DEFAULT FALSE,
                    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建云端邮件表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS cloud_mails (
                    mail_id VARCHAR(50) PRIMARY KEY,
                    recipient_id VARCHAR(50) NOT NULL,
                    sender_id VARCHAR(50) DEFAULT 'system',
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    has_rewards BOOLEAN DEFAULT FALSE,
                    rewards JSONB,
                    status VARCHAR(20) DEFAULT 'unread',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP,
                    expires_at TIMESTAMP,
                    FOREIGN KEY (recipient_id) REFERENCES users(user_id)
                )
            ''')
            
            # 创建聊天消息表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS chat_messages (
                    message_id VARCHAR(50) PRIMARY KEY,
                    sender_id VARCHAR(50) NOT NULL,
                    sender_name VARCHAR(50) NOT NULL,
                    message TEXT NOT NULL,
                    channel VARCHAR(50) DEFAULT 'global',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (sender_id) REFERENCES users(user_id)
                )
            ''')
            
            # 创建在线状态表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id VARCHAR(50) PRIMARY KEY,
                    user_id VARCHAR(50) NOT NULL,
                    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 创建索引
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_cloud_mails_recipient ON cloud_mails(recipient_id)')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_cloud_mails_status ON cloud_mails(status)')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_chat_messages_channel ON chat_messages(channel)')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at DESC)')
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.get("/")
        async def root():
            return {"message": "传奇游戏云端服务器运行中", "version": "1.0.0"}
        
        @self.app.post("/auth/login")
        async def login(user_data: UserLogin):
            """用户登录"""
            try:
                async with self.db_optimizer.get_async_connection() as conn:
                    user = await conn.fetchrow(
                        "SELECT user_id, username FROM users WHERE username = $1 AND password_hash = $2",
                        user_data.username, self._hash_password(user_data.password)
                    )
                    
                    if not user:
                        raise HTTPException(status_code=401, detail="用户名或密码错误")
                    
                    # 更新在线状态
                    await conn.execute(
                        "UPDATE users SET is_online = TRUE, last_login = CURRENT_TIMESTAMP WHERE user_id = $1",
                        user['user_id']
                    )
                    
                    # 创建JWT token
                    token_data = {
                        "user_id": user['user_id'],
                        "username": user['username'],
                        "exp": datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
                    }
                    access_token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
                    
                    return {
                        "access_token": access_token,
                        "token_type": "bearer",
                        "user_id": user['user_id'],
                        "username": user['username']
                    }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")
        
        @self.app.post("/auth/register")
        async def register(user_data: UserLogin):
            """用户注册"""
            try:
                user_id = str(uuid.uuid4())
                password_hash = self._hash_password(user_data.password)
                
                async with self.db_optimizer.get_async_connection(for_write=True) as conn:
                    await conn.execute(
                        "INSERT INTO users (user_id, username, password_hash) VALUES ($1, $2, $3)",
                        user_id, user_data.username, password_hash
                    )
                    
                    return {"message": "注册成功", "user_id": user_id}
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"注册失败: {str(e)}")
        
        @self.app.get("/mail/list")
        async def get_mail_list(current_user: dict = Depends(self._get_current_user)):
            """获取邮件列表"""
            try:
                async with self.db_optimizer.get_async_connection() as conn:
                    mails = await conn.fetch('''
                        SELECT mail_id, title, sender_id, has_rewards, status, created_at, expires_at
                        FROM cloud_mails 
                        WHERE recipient_id = $1 AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
                        ORDER BY created_at DESC
                    ''', current_user['user_id'])
                    
                    return [dict(mail) for mail in mails]
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取邮件列表失败: {str(e)}")
        
        @self.app.get("/mail/{mail_id}")
        async def get_mail_detail(mail_id: str, current_user: dict = Depends(self._get_current_user)):
            """获取邮件详情"""
            try:
                async with self.db_optimizer.get_async_connection(for_write=True) as conn:
                    mail = await conn.fetchrow('''
                        SELECT * FROM cloud_mails 
                        WHERE mail_id = $1 AND recipient_id = $2
                    ''', mail_id, current_user['user_id'])
                    
                    if not mail:
                        raise HTTPException(status_code=404, detail="邮件不存在")
                    
                    # 标记为已读
                    if mail['status'] == 'unread':
                        await conn.execute(
                            "UPDATE cloud_mails SET status = 'read', read_at = CURRENT_TIMESTAMP WHERE mail_id = $1",
                            mail_id
                        )
                    
                    return dict(mail)
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取邮件详情失败: {str(e)}")
        
        @self.app.post("/mail/send")
        async def send_mail(mail_data: MailCreate, current_user: dict = Depends(self._get_current_user)):
            """发送邮件"""
            try:
                mail_id = str(uuid.uuid4())
                expires_at = datetime.now() + timedelta(hours=mail_data.expire_hours)
                
                async with self.db_optimizer.get_async_connection(for_write=True) as conn:
                    await conn.execute('''
                        INSERT INTO cloud_mails (mail_id, recipient_id, sender_id, title, content, 
                                               has_rewards, rewards, expires_at)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ''', mail_id, mail_data.recipient_id, current_user['user_id'], 
                        mail_data.title, mail_data.content, 
                        mail_data.rewards is not None, 
                        json.dumps(mail_data.rewards) if mail_data.rewards else None,
                        expires_at)
                    
                    return {"message": "邮件发送成功", "mail_id": mail_id}
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"发送邮件失败: {str(e)}")
        
        @self.app.post("/mail/{mail_id}/action")
        async def mail_action(mail_id: str, action: MailAction, current_user: dict = Depends(self._get_current_user)):
            """邮件操作（领取奖励、删除等）"""
            try:
                async with self.db_optimizer.get_async_connection(for_write=True) as conn:
                    mail = await conn.fetchrow(
                        "SELECT * FROM cloud_mails WHERE mail_id = $1 AND recipient_id = $2",
                        mail_id, current_user['user_id']
                    )
                    
                    if not mail:
                        raise HTTPException(status_code=404, detail="邮件不存在")
                    
                    if action.action == "claim" and mail['has_rewards'] and mail['status'] != 'claimed':
                        # 领取奖励逻辑
                        await conn.execute(
                            "UPDATE cloud_mails SET status = 'claimed' WHERE mail_id = $1",
                            mail_id
                        )
                        return {"message": "奖励领取成功", "rewards": json.loads(mail['rewards']) if mail['rewards'] else None}
                    
                    elif action.action == "delete":
                        await conn.execute("DELETE FROM cloud_mails WHERE mail_id = $1", mail_id)
                        return {"message": "邮件删除成功"}
                    
                    else:
                        raise HTTPException(status_code=400, detail="无效的操作")
                        
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"邮件操作失败: {str(e)}")
        
        @self.app.websocket("/ws/chat")
        async def websocket_chat(websocket: WebSocket):
            """WebSocket聊天连接"""
            await websocket.accept()
            client_id = str(uuid.uuid4())
            
            try:
                # 验证用户身份（这里简化处理）
                self.connected_clients[client_id] = websocket
                self.chat_channels["global"].add(client_id)
                
                while True:
                    data = await websocket.receive_text()
                    message_data = json.loads(data)
                    
                    # 保存聊天消息到数据库
                    await self._save_chat_message(message_data)
                    
                    # 广播消息给所有在线用户
                    await self._broadcast_message(message_data, channel="global")
                    
            except Exception as e:
                print(f"WebSocket连接错误: {e}")
            finally:
                # 清理连接
                if client_id in self.connected_clients:
                    del self.connected_clients[client_id]
                self.chat_channels["global"].discard(client_id)
        
        @self.app.get("/chat/history")
        async def get_chat_history(limit: int = 50, current_user: dict = Depends(self._get_current_user)):
            """获取聊天历史"""
            try:
                async with self.db_optimizer.get_async_connection() as conn:
                    messages = await conn.fetch('''
                        SELECT sender_name, message, created_at 
                        FROM chat_messages 
                        WHERE channel = 'global'
                        ORDER BY created_at DESC 
                        LIMIT $1
                    ''', limit)
                    
                    return [dict(msg) for msg in reversed(messages)]
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取聊天历史失败: {str(e)}")
    
    async def _save_chat_message(self, message_data: dict):
        """保存聊天消息到数据库"""
        try:
            message_id = str(uuid.uuid4())
            async with self.db_optimizer.get_async_connection(for_write=True) as conn:
                await conn.execute('''
                    INSERT INTO chat_messages (message_id, sender_id, sender_name, message, channel)
                    VALUES ($1, $2, $3, $4, $5)
                ''', message_id, message_data.get('sender_id', 'unknown'), 
                    message_data.get('sender_name', '匿名'), message_data.get('message', ''), 
                    message_data.get('channel', 'global'))
        except Exception as e:
            print(f"保存聊天消息失败: {e}")
    
    async def _broadcast_message(self, message_data: dict, channel: str = "global"):
        """广播消息给频道内所有用户"""
        if channel in self.chat_channels:
            disconnected_clients = []
            for client_id in self.chat_channels[channel]:
                if client_id in self.connected_clients:
                    try:
                        await self.connected_clients[client_id].send_text(json.dumps(message_data))
                    except:
                        disconnected_clients.append(client_id)
            
            # 清理断开的连接
            for client_id in disconnected_clients:
                self.connected_clients.pop(client_id, None)
                self.chat_channels[channel].discard(client_id)
    
    def _hash_password(self, password: str) -> str:
        """密码哈希（简化版本，生产环境请使用bcrypt）"""
        import hashlib
        return hashlib.sha256(password.encode()).hexdigest()
    
    async def _get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        """获取当前用户信息"""
        try:
            payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = payload.get("user_id")
            username = payload.get("username")
            
            if user_id is None or username is None:
                raise HTTPException(status_code=401, detail="无效的token")
            
            return {"user_id": user_id, "username": username}
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token已过期")
        except jwt.JWTError:
            raise HTTPException(status_code=401, detail="无效的token")

# 服务器实例
server = CloudGameServer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    await server.initialize()
    yield
    # 关闭时清理资源
    if server.redis_client:
        await server.redis_client.close()

# 应用实例
app = server.app
app.router.lifespan_context = lifespan

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("cloud_server:app", host="0.0.0.0", port=8000, reload=True) 