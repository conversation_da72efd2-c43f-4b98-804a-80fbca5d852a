#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主游戏模块
from main import Game
import tkinter as tk

def test_load_game():
    """测试load_game方法"""
    print("🧪 开始测试load_game方法...")
    
    # 创建一个简单的tkinter根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏窗口
    
    # 创建游戏实例
    game = Game(root)
    
    # 调用load_game方法
    print("🔍 调用game.load_game()...")
    game.load_game()
    
    print("✅ 测试完成")
    root.destroy()

if __name__ == "__main__":
    test_load_game()
